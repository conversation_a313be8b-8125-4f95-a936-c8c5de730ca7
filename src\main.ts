import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import axios from 'axios'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import Vue3CountTo from 'vue3-count-to'
import './style.css'
const app = createApp(App)
app.component('CountTo', Vue3CountTo)
app.config.globalProperties.$axios = axios
app.use(Antd);
app.use(router)
.mount('#app')
