<template>
  <div ref="mapChart" id="mapChart" class="mapChart" style="width: 100%;height: 700px;" />
</template>
    <script setup lang="ts" name="Map">
import type { VNode } from "vue";
import { ref, onMounted } from "vue";

import { createVNode, render, watch } from "vue";
import * as echarts from "echarts";
import geoJson from "../assets/shanghai.json"; // 为刚刚下载的json文件，需正确引入路径
// import "echarts-gl";
// import echartsGL from "echarts-gl";
import mapBgImg from "../assets/map.png";
import dw from "../assets/dw.png";
import dw1 from "../assets/dw1.png";
import dw2 from "../assets/dw2.png";
import { require } from "../utils/require";
const mapChart = ref<HTMLElement>();
const props = defineProps({
  height: {
    type: String,
    default: "573",
  },
});
watch(
  () => props.height,
  (newHeight) => {
    // alert(2)
    mapChart.value.style.height = `${newHeight}px`; // 更新容器高度
    // alert(2)
    if (myChart) {
      myChart.resize(); // 更新图表大小以匹配容器大小变化
    }
  }
);
const emit = defineEmits(["city", "changeStatus"]);
const showData = ref(true);
// const emit = defineEmits(["changeStatus"]);
let myChart = null;
// const mapCharts = ref(null);
const initMap = async (status?: Boolean) => {
  myChart = echarts.init(mapChart.value);
  echarts.registerMap("shandong", geoJson);
  // 绘制图表
  let option = {
//    backgroundColor: '#090F27',
   tooltip: {
      triggerOn: "onmousemove",
   },
   legend: {
      show: false
   },
   geo: [
      {
         type: "map",
         map: 'shandong',
         label:{},
         aspectScale: 1,
         zoom: 1.2,
         emphasis: {
            disabled: true,
         },
         z: 98,
         itemStyle: {
            shadowColor: 'rgba(22, 118, 179)',
            shadowBlur: 2,
            shadowOffsetX: -10,
            shadowOffsetY: 6
         },
 // lambertMaterial: {
      //   detailTexture: mapBgImg,
      // },
         tooltip: { show: false }
      },
    ],
   
   series: [
      {
         type: "map",
         map: 'shandong',
         roam: true,
         z: 100,
         aspectScale: 1,
         label:{
            show:true,
            color:'white'
         },
        zoom: 1.2,
         itemStyle: {
            normal: {
            areaColor: {
              image: mapBgImg,
            },
            borderColor: "#fff",
            color: "#FFFFFF",
            borderWidth: 0.2,
          },
         },
         // 鼠标悬浮的操作；也可以设置鼠标悬浮的样式同select一样
         emphasis: {
            label: {
               show: true
            },
            itemStyle: {
               areaColor: 'yellow'
            }
         },
         // 鼠标点击选中的样式
         select: {
            label: {
            // 选中区域的label(文字)样式
            color: "#fff",
            fontSize: "20px",
          },
          itemStyle: {
            // 选中区域的默认样式
            // areaColor: '#0075FF'
          },
         },
         tooltip: { show: false },
         data: [],
      },
      {
          name: "点2",
          type: "scatter",
          coordinateSystem: "geo",
          symbol:  'image://'+ require('../assets/dw.png'), //气泡
          symbolSize: [33, 38],
              symbolOffset: [0, -10],
          label: {
            // 点击标记点显示队伍名称
            show: false,
            position: "top",
            distance: -60,
            // textStyle: {
            //   color: "rgba(255, 255, 255, 1)",
            //   fontSize: 14,
            //   padding: [15, 80],
            //   backgroundColor: {
            //     image: dw1,
            //   },
            // },
          },
          itemStyle: {
            //  color: '#F62157' //标志颜色
          },
          zlevel: 60,
          data: [[121.43752, 31.179973]],
        },
        {
          name: "点1",
          type: "scatter",
          coordinateSystem: "geo",
          symbol:  'image://'+ require('../assets/dw1.png'), //气泡
          symbolSize: [133, 58],
              symbolOffset: [0, -40],
          label: {
            // 点击标记点显示队伍名称
            show: false,
            position: "top",
            distance: -60,
            formatter(params) {
              return "市局";
            },
            // textStyle: {
            //   color: "rgba(255, 255, 255, 1)",
            //   fontSize: 14,
            //   padding: [15, 80],
            //   backgroundColor: {
            //     image: dw1,
            //   },
            // },
          },
          itemStyle: {
            //  color: '#F62157' //标志颜色
          },
          zlevel: 60,
          data: [[121.43752, 31.179973]],
        },
        
   ]
};
let bu = 0;
requestAnimationFrame(() => {
//    setInterval(() => {
//       if (bu > 23 + 4 + 2 + 5) {
//          bu = 0
//       }
//       myChart.dispatchAction({
//          type: 'highlight',//默认显示江苏的提示框
//          seriesIndex: 0,//这行不能省
//          dataIndex: bu
//       });
//       myChart.dispatchAction({
//          type: 'downplay',//默认显示江苏的提示框
//          seriesIndex: 0,//这行不能省
//          dataIndex: bu - 1
//       });
//       bu++
//    }, 1000)
})

myChart.on('georoam', (event) => {
   let center = myChart.getOption().series[0].center;
   let zoom = myChart.getOption().series[0].zoom;
   myChart.setOption({
      geo: [
         {
            zoom,
            center,
            animation: false
         }
      ]
   })
})
  const index = ref();

  myChart.on("click", function (params) {
    click_type = true;
    emit("changeStatus", false);
    index.value = params.dataIndex;
    emit("city", { data: { name: params.name } });
    myChart.setOption(option, false);
  });
  let click_type;
  myChart.getZr().on("click", (params) => {
    click_type = false;
    setTimeout(check, 500);
    function check() {
      if (click_type != false) return;
      if (!params.target) {
        if (click_type != false) return;
        emit("city", { data: { name: "上海市" } });
        myChart.setOption(option, true);
        // initMap()
        emit("changeStatus", true);
        return;
      }
    }
  });
  myChart.getZr().on("dblclick", (params) => {
    click_type = true;
    if (!params.target) {
      emit("city", { data: { name: "上海市" } });
      myChart.setOption(option, true);
      emit("changeStatus", false);
      return;
      // initMap()
    }
  });
  myChart.setOption(option);
  // window.addEventListener('resize',()=>{
  //   myChart.resize()

  // },false)
  //   highlightNext();
};
// const emit = defineEmits(["changeStatus"]);
const mapChartSize = () => {
  if (myChart) {
    myChart.resize(); // 图表自适应大小
  }
};
defineExpose({
  initMap,
});
onMounted(() => {
  // 初始化地图
  initMap();
  mapChartSize();
});
</script>