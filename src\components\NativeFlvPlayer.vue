<template>
  <div class="flv-player-container">
    <video
      ref="videoElement"
      class="flv-video"
      controls
      autoplay
      muted
      playsinline
    ></video>
    <div v-if="loading" class="loading-indicator">加载中...</div>
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import flvjs from 'flv.js';

const props = defineProps({
  url: {
    type: String,
    required: true,
    validator: (value: string) => value.startsWith('http') || value.startsWith('ws')
  },
  isLive: {
    type: Boolean,
    default: true
  }
});

const videoElement = ref<HTMLVideoElement | null>(null);
const flvPlayer = ref<flvjs.Player | null>(null);
const loading = ref(true);
const error = ref('');

// 初始化播放器
const initPlayer = () => {
  // 清除之前的实例
  destroyPlayer();

  // 检查浏览器支持
  if (!flvjs.isSupported()) {
    error.value = '当前浏览器不支持FLV播放，请使用Chrome/Firefox/Edge';
    return;
  }

  if (!videoElement.value || !props.url) return;

  try {
    // 创建FLV播放器实例
    flvPlayer.value = flvjs.createPlayer({
      type: 'flv',
      url: 'ws://demo.witarchive.com:3080/ws/hlsram/live0.flv',
      isLive: props.isLive,
      cors: true,
      withCredentials: false
    }, {
      enableWorker: true,
      enableStashBuffer: !props.isLive, // 直播禁用缓存
      autoCleanupSourceBuffer: true,
      fixAudioTimestampGap: true
    });

    // 附加到video元素
    flvPlayer.value.attachMediaElement(videoElement.value);

    // 加载媒体
    // flvPlayer.value.load().then(() => {
    //   loading.value = false;
    //   console.log('FLV媒体加载成功');
    // }).catch(err => {
    //   loading.value = false;
    //   error.value = `加载失败: ${err.message}`;
    //   console.error('FLV加载错误:', err);
    // });

    // // 事件监听
    // flvPlayer.value.on('error', handleError);
    // flvPlayer.value.on('ended', () => !props.isLive && destroyPlayer());
    // flvPlayer.value.on('play', () => loading.value = false);
  } catch (err: any) {
    loading.value = false;
    error.value = `初始化失败: ${err.message}`;
    console.error('播放器初始化错误:', err);
  }
};

// 错误处理
const handleError = (err: any) => {
  error.value = `播放错误: ${err.type || '未知错误'}`;
  console.error('FLV播放错误:', err);
  // 直播流错误时尝试重新连接
  if (props.isLive) {
    setTimeout(initPlayer, 3000);
  }
};

// 销毁播放器
const destroyPlayer = () => {
  if (flvPlayer.value) {
    flvPlayer.value.off('error', handleError);
    flvPlayer.value.destroy();
    flvPlayer.value = null;
  }
};

// 监听URL变化
watch(
  () => props.url,
  () => initPlayer(),
  { immediate: true }
);

onMounted(() => {
  // 确保DOM加载完成
  if (videoElement.value) {
    initPlayer();
  }
});

onBeforeUnmount(() => {
  destroyPlayer();
});
</script>

<style scoped>
.flv-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.flv-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  background: rgba(0,0,0,0.5);
  padding: 8px 16px;
  border-radius: 4px;
}

.error-message {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: red;
  background: rgba(0,0,0,0.7);
  padding: 8px 16px;
  border-radius: 4px;
}
</style>