<template>
  <div class="carouselChart" ref="carouselChart">
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import * as echarts from "echarts";
import { transform } from "typescript";
// import
import pieCenter from "../assets/pieCenter.png";

const props = defineProps({
  selShow: {
    type: Boolean,
    default: true,
  },
  carouselData: {
    type: Object,
    default: () => {},
  },
});
const carouselChart = ref(null);
const initMap = () => {
  const chart = echarts.init(carouselChart.value);
  var colors = ["#FF1919", "#F8CF04", "#55FF00", "#03A0EF"];
  const value = 54;
  const maxvalue = 100;
  var fontColor = "#000000";
  var data = [70, 2];
  var fontSize = 13;
  var percent = (data[0] / (data[0] + data[1])) * 100;
  const val1 = 54;
  const maxV = 100;
  const minV = 0;
  const xxx = maxV * 2 - val1;

  const option = {
    // backgroundColor:"#05233b",
    title: {
      text: props.carouselData.roomName,
      left: "center",
      top: 10,
      textStyle: {
        color: "#FFFFFF",
        fontSize: 16, //表盘上的标题文字大小
        fontWeight: 400,
        fontFamily: "PingFangSC",
      },
    },
    graphic: [
      {
        z: 4,
        type: "image",
        id: "logo",
        left: "34%", //调整图片位置
        top: "35%", //调整图片位置
        // z: -10,
        bounding: "raw",
        rotation: 0, //旋转
        origin: [64.5, 32.5], //中心点
        scale: [1.0, 1.0], //缩放
        //设置图片样式
        style: {
          image: pieCenter,
          width: 136,
          height: 136,
          opacity: 1,
        },
      },
      {
        type: "text",
        left: "45%",
        top: "57%",
        style: {
          text: props.carouselData.score_value + "%",
          textAlign: "center",
          fill: "rgba(251, 233, 75, 1)",
          fontSize: 16,
        },
      },
      {
        type: "text",
        left: "47%",
        top: "65%",
        style: {
          text: props.carouselData.score_state,
          textAlign: "center",
          fill: "rgba(251, 233, 75, 1)",
          fontSize: 16,
        },
      },
    ],
    series: [
      //最外城圆圈
      {
        type: "gauge",
        name: "外层辅助",
        radius: "86%",
        center: ["50%", "60%"],
        pointer: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: "#061B25",
                  },
                  {
                    offset: 0.3,
                    color: "#0F3346",
                  },
                  {
                    offset: 0.5,
                    color: "#18465D",
                  },
                  {
                    offset: 0.8,
                    color: "#0F3346",
                  },
                  {
                    offset: 1,
                    color: "#061B25",
                  },
                ]),
              ],
            ],
            width: 2,
            opacity: 1,
          },
        },
        splitLine: {
          show: false, //是否显示分隔线
          distance: -30,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },

      // 值得尾巴，那个圆圈
      // {
      //   type: "pie",
      //   //   radius: ['31%', '37%'],
      //   radius: "79%",
      //   center: ["50%", "50%"],
      //   roseType: "radius",
      //   silent: true,
      //   startAngle: 180,
      //   z: 20,
      //   data: [
      //     {
      //       name: "",
      //       value: val1,
      //       label: {
      //         show: false,
      //       },
      //       labelLine: {
      //         show: false,
      //       },
      //       itemStyle: {
      //         color: "rgba(0,0,0,0)",
      //       },
      //     },

      //     // 画中间的图标
      //     {
      //       name: "",
      //       value: 0,
      //       label: {
      //         position: "inside",
      //         // backgroundColor: 'transform',
      //         borderRadius: 5,
      //         padding: 5, // 可以控制圆的大小
      //         borderWidth: 2,
      //         borderColor: "#ffffff",
      //       },
      //     },
      //     {
      //       name: "",
      //       value: val1,
      //       label: {
      //         show: false,
      //       },
      //       labelLine: {
      //         show: false,
      //       },
      //       itemStyle: {
      //         color: "rgba(255,255,255,0)",
      //       },
      //     },
      //   ],
      // },
      {
        name: "灰色内圈",
        type: "gauge",
        radius: "79%",
        min: 0,
        max: 100,
        center: ["50%", "60%"],
        axisLine: {
          roundCap: true,
          lineStyle: {
            color: [
              [value / maxvalue, colors[0]],
              [1, "#1C292F"],
            ],
            // fontSize: 10,
            width: 8,
            opacity: 1,
          },
        },
        progress: {
          show: true, //是否显示进度条
          roundCap: true, //是否在两端显示成圆形
          width: 8, //进度条宽度
          itemStyle: {
            color: {
              colorStops: [
                {
                  offset: 0,
                  color: colors[0], // 0% 处的颜色
                },
                {
                  offset: 0.3,
                  color: colors[1], // 100% 处的颜色
                },
                {
                  offset: 0.75,
                  color: colors[2], // 100% 处的颜色
                },
                {
                  offset: 1,
                  color: colors[3], // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
        },

        //仪表盘指针。
        pointer: {
          //这个show属性好像有问题，因为在这次开发中，需要去掉指正，我设置false的时候，还是显示指针，估计是BUG吧，我用的echarts-3.2.3；希望改进。最终，我把width属性设置为0，成功搞定！
          show: false,
        },
        splitLine: {
          length: 10,
          lineStyle: {
            width: 2,
            color: "#4ABCF5",
          },
        },
        //刻度标签。
        axisLabel: {
          show: false,
        },
        itemStyle: {
          normal: {
            color: "rgb(0,191,255)",
          },
        },
        detail: {
          formatter: function () {
            return "正在监管中";
          },
          offsetCenter: [0, 80],
          textStyle: {
            padding: [0, 0, 0, 0],
            fontSize: 13,
            fontWeight: "500",
            color: "#96A9BA",
          },
        },
        title: {
          //标题
          show: true,
          offsetCenter: [0, 50], // x, y，单位px
          textStyle: {
            color: "#ffffff",
            fontSize: 30, //表盘上的标题文字大小
            fontWeight: 700,
            fontFamily: "PingFangSC",
          },
        },
        data: [
          {
            value: props.carouselData.score_value,
          },
        ],
      },
    ],
  };
  option && chart.setOption(option, true);
};
nextTick(() => {
  initMap(); //基于最新的DOM状态执行update方法
});
onMounted(async () => {
  await nextTick();
  // initMap();
});
</script>
<style scoped>
.carouselChart {
  width: 100%;
  height: 100%;
}
</style>