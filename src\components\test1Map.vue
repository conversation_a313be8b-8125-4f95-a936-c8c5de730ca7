<template>
  <div ref="mapChart" id="mapChart" class="mapChart" style="width: 100%;height: 700px;" />
</template>
  <script setup lang="ts" name="Map">
import type { VNode } from "vue";
import { ref, onMounted } from "vue";

import { createVNode, render, watch } from "vue";
import * as echarts from "echarts";
import geoJson from "../assets/shanghai.json"; // 为刚刚下载的json文件，需正确引入路径
import "echarts-gl";
import echartsGL from "echarts-gl";
import mapBgImg from "../assets/map.png";
import dw from "../assets/dw.png";
import dw1 from "../assets/dw1.png";
import dw2 from "../assets/dw2.png";
import {require} from '../utils/require'
const mapChart = ref<HTMLElement>();
const props = defineProps({
  height: {
    type: String,
    default: "573",
  },
});
watch(
  () => props.height,
  (newHeight) => {
    // alert(2)
    mapChart.value.style.height = `${newHeight}px`; // 更新容器高度
    // alert(2)
    if (myChart) {
      myChart.resize(); // 更新图表大小以匹配容器大小变化
    }
  }
);
const emit = defineEmits(["city", "changeStatus"]);
const showData = ref(true);
// const emit = defineEmits(["changeStatus"]);
let myChart = null;
// const mapCharts = ref(null);
const initMap = async (status?: Boolean) => {
  myChart = echarts.init(mapChart.value);
  echarts.registerMap("shanghai", geoJson);
  // 绘制图表
  let option = {
    // 悬浮窗
    tooltip: {
      trigger: "item",
      formatter: function (point, params, dom, rect, size) {
        //这里可以自定义浮窗的dom节点及样式
        return `${params.name}: ${params.value || 0}`;
      },
    },
    geo: [
      {
        map: "shanghai",
        aspectScale: 1,
        zoom: 0.6,
        layoutCenter: ["50%", "48%"],
        layoutSize: "180%",
        show: true,
        roam: false,
        label: {
          emphasis: {
            show: false,
          },
        },
        tooltip: {
          show: false,
        },
        itemStyle: {
          normal: {
            borderColor: "#c0f3fb",
            image: mapBgImg,
            borderWidth: 1,
            shadowColor: "#8cd3ef",
            shadowOffsetY: 10,
            shadowBlur: 120,
            areaColor: "transparent",
          },
        },
      },
      // 重影
      {
        type: "map",
        map: "shanghai",
        zlevel: -1,
        aspectScale: 1,
        zoom: 0.6,
        layoutCenter: ["50%", "49%"],
        layoutSize: "180%",
        roam: false,
        silent: true,
        tooltip: {
          show: false,
        },
        itemStyle: {
          normal: {
            borderWidth: 1,
            // borderColor:"rgba(17, 149, 216,0.6)",
            borderColor: "rgba(58,149,253,0.8)",
            shadowColor: "rgba(172, 122, 255,0.5)",
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: "rgba(5,21,35,0.1)",
          },
        },
      },
      {
        type: "map",
        map: "shanghai",
        zlevel: -2,
        aspectScale: 1,
        zoom: 0.6,
        layoutCenter: ["50%", "50%"],
        layoutSize: "180%",
        roam: false,
        silent: true,
        tooltip: {
          show: false,
        },
        itemStyle: {
          normal: {
            borderWidth: 1,
            // borderColor: "rgba(57, 132, 188,0.4)",
            borderColor: "rgba(58,149,253,0.6)",
            shadowColor: "rgba(65, 214, 255,1)",
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: "transpercent",
          },
        },
      },
      {
        type: "map",
        map: "shanghai",
        zlevel: -3,
        aspectScale: 1,
        zoom: 0.6,
        layoutCenter: ["50%", "51%"],
        layoutSize: "180%",
        roam: false,
        silent: true,
        tooltip: {
          show: false,
        },
        itemStyle: {
          normal: {
            borderWidth: 1,
            // borderColor: "rgba(11, 43, 97,0.8)",
            borderColor: "rgba(58,149,253,0.4)",
            shadowColor: "rgba(58,149,253,1)",
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: "transpercent",
          },
        },
      },
      {
        type: "map",
        map: "shanghai",
        zlevel: -4,
        aspectScale: 1,
        zoom: 0.6,
        layoutCenter: ["50%", "52%"],
        layoutSize: "180%",
        roam: false,
        silent: true,
        tooltip: {
          show: false,
        },
        itemStyle: {
          normal: {
            borderWidth: 5,
            image: mapBgImg,
            // borderColor: "rgba(11, 43, 97,0.8)",
            borderColor: "rgba(5,9,57,0.8)",
            shadowColor: "rgba(29, 111, 165,0.8)",
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: "rgba(5,21,35,0.1)",
          },
        },
      },
    ],
    // 数据
    series: [
      {
        name: "上海市数据",
        type: "map",
        map: "shanghai", // 自定义扩展图表类型
        aspectScale: 1,
        zoom: 0.6, // 缩放
        showLegendSymbol: true,
        zlevel: 100,

        tooltip: {
          show: false,
        },
        label: {
          show: false,
          normal: {
            show: true,
            textStyle: { color: "#fff", fontSize: "120%" },
          },
          emphasis: {
            // show: false,
          },
        },
        itemStyle: {
          normal: {
            areaColor: {
              image: mapBgImg,
            },
            borderColor: "#fff",
            color: "#FFFFFF",
            borderWidth: 0.2,
          },
          emphasis: {
            show: false,
            color: "#fff",
            areaColor: "rgba(0,254,233,0.6)",
          },
        },
        // itemStyle: {
        //   normal: {
        //     areaColor: {
        //       type: "linear",
        //       x: 1200,
        //       y: 0,
        //       x2: 0,
        //       y2: 0,
        //       colorStops: [
        //         {
        //           offset: 0,
        //           color: "rgba(3,27,78,0.75)", // 0% 处的颜色
        //         },
        //         {
        //           offset: 1,
        //           color: "rgba(58,149,253,0.75)", // 50% 处的颜色
        //         },
        //       ],
        //       global: true, // 缺省为 false
        //     },
        //     borderColor: "#fff",
        //     borderWidth: 0.2,
        //   },
        //   emphasis: {
        //     show: false,
        //     color: "#fff",
        //     areaColor: "rgba(0,254,233,0.6)",
        //   },
        // },
        layoutCenter: ["50%", "48%"],
        layoutSize: "180%",
        select: {
          // 地图选中区域样式
          label: {
            // 选中区域的label(文字)样式
            color: "#fff",
            fontSize: "20px",
          },
          itemStyle: {
            // 选中区域的默认样式
            // areaColor: '#0075FF'
          },
        },
        markPoint: {
          symbol: "none",
        },
        // data: data,
      },
      //       {
      //     type: "map",
      //     map: "shanghai",
      //     roam: true, //是否开启鼠标缩放和平移漫游
      //     animationDurationUpdate: 0,
      //     zoom: 1.15,
      //     layoutSize: "100",
      //     label: {
      //       normal: {
      //         show: true,
      //         fontSize: 14,
      //         color: "#fff",
      //       },
      //       emphasis: {
      //         color: "#fff",
      //       },
      //     },
      //     itemStyle: {
      //       normal: {
      //         areaColor: {
      //           image: mapBgImg,
      //           repeat: "repeat-x"
      //         },
      //         borderColor: "#678BBB", //省份边框颜色
      //         borderWidth: 2, // 省份边框宽度
      //         shadowBlur: 0,
      //         shadowOffsetX: 0,
      //         shadowOffsetY: 0
      //       },
      //       emphasis: {
      //         areaColor: {
      //           type: 'linear',
      //           x: 0,
      //           y: 0,
      //           x2: 0,
      //           y2: 1,
      //           colorStops: [{
      //             offset: 0, color: 'rgba(45, 124, 200, 1)' // 渐变起始颜色
      //           }, {
      //             offset: 1, color: 'rgba(75, 175, 247, 1)' // 渐变结束颜色
      //           }]
      //         },
      //         borderColor: "#fff", //省份边框颜色
      //         borderWidth: 1, // 高亮时的边框宽度
      //       },
      //     },
      //     select: {
      //       label: {
      //         show: true,
      //         color: "#fff",
      //       },
      //       itemStyle: {
      //         areaColor: "#123972", // 高亮时候地图显示的颜色
      //         borderWidth: 0, // 高亮时的边框宽度
      //       },
      //     },
      //   },
      // {
      //   type: 'scatter',
      //   itemStyle: {
      //     color: '#efe244',
      //     opacity: 1,
      //     shadowColor: 'rgba(255, 248, 74, 0.8)',
      //     shadowBlur: 10,
      //     shadowOffsetX: 1,
      //     shadowOffsetY: 1
      //   },
      //   symbol: 'diamond',
      //   coordinateSystem: 'geo',
      //   zlevel: 100,
      //   geoIndex: 0,
      //   top: 10,
      //   symbolSize: 10,
      // //   data: seriesData,
      // }
    ],
  };
  const index = ref();

  myChart.on("click", function (params) {
    click_type = true;
    emit("changeStatus", false);
    index.value = params.dataIndex;
    emit("city", { data: { name: params.name } });
    myChart.setOption(option, false);
  });
  let click_type;
  myChart.getZr().on("click", (params) => {
    click_type = false;
    setTimeout(check, 500);
    function check() {
      if (click_type != false) return;
      if (!params.target) {
        if (click_type != false) return;
        emit("city", { data: { name: "上海市" } });
        myChart.setOption(option, true);
        // initMap()
        emit("changeStatus", true);
        return;
      }
    }
  });
  myChart.getZr().on("dblclick", (params) => {
    click_type = true;
    if (!params.target) {
      emit("city", { data: { name: "上海市" } });
      myChart.setOption(option, true);
      emit("changeStatus", false);
      return;
      // initMap()
    }
  });
  myChart.setOption(option);
  // window.addEventListener('resize',()=>{
  //   myChart.resize()

  // },false)
  //   highlightNext();
};
// const emit = defineEmits(["changeStatus"]);
const mapChartSize = () => {
  if (myChart) {
    myChart.resize(); // 图表自适应大小
  }
};
defineExpose({
  initMap,
});
onMounted(() => {
  // 初始化地图
  initMap();
  mapChartSize();
});
</script>