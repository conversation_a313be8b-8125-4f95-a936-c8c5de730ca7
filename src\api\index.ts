import request from "../utils/request";

//供应商用户登录
export const reqRoleGetAll = (data?: any) =>
  request.post("/api/user/login", data);
//左边】查询档案类型增量查询
export const archives_type_increment = (params?: any) => {
  return request.get("/api/large/archives_type_increment", { params: params });
};
//【左边】查询档案类型占比情况
export const archives_type_ratio = (params?: any) => {
  return request.get("/api/large/archives_all_type_ratio", { params: params });
};
//【中间】查询利用情况集合
export const archives_use_list = (params?: any) => {
  return request.get("/api/large/archives_use_list", { params: params });
};
//【左边】查询利用情况折线图
export const archives_use_situation = (params?: any) => {
  return request.get("/api/large/archives_use_situation", { params: params });
};
//【中间】查询馆藏区域树形
export const collection_area_tree = (params?: any) => {
  return request.get("/api/large/collection_area_tree", { params: params });
};
//【中间】查询馆藏数量统计
export const collection_total = (params?: any) => {
  return request.get("/api/large/collection_total", { params: params });
};
//【右边】查询库房摄像头信息
export const storeroom_camera = (params?: any) => {
  return request.get("/api/large/storeroom_camera", { params: params });
};
//【中间】查询馆藏区域树形
export const storeroom_environment = (params?: any) => {
  return request.get("/api/large/storeroom_environment", { params: params });
};
//【中间】查询馆藏区域树形
export const collection_area_list = (params?: any) => {
  return request.get("/api/large/collection_area_list", { params: params });
};
//【中间】查询文书类利用情况集合
export const archives_use_document_list = (params?: any) => {
  return request.get("/api/large/archives_use_document_list", {
    params: params,
  });
};
//【右边】档案检索列表
export const archives_list = (params?: any) => {
  return request.get("/api/large/archives_list", { params: params });
};
//【右边】档案检索列表
export const device_camera_list = (params?: any) => {
  return request.get("/api/large/device_camera_list", { params: params });
};
//【右边】档案检索列表
export const fault_one = (params?: any) => {
  return request.get("/api/large/fault_one", { params: params });
};
export const archives_open_shelf = (data?: any) =>
  request.post("/api/large/archives_open_shelf", data);
export const get_camera_url = (params?: any) => {
  return request.get("/api/large/get_camera_url", { params: params });
};
export const device_camera_batch_show = (data?: any) =>
  request.post("/api/large/device_camera_batch_show", data);
