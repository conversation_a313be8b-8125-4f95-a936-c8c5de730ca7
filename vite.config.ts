import { defineConfig } from 'vite';
import { nodePolyfills } from 'vite-plugin-node-polyfills';
import vue from '@vitejs/plugin-vue';
import inject from '@rollup/plugin-inject';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  base:'./',
  plugins: [
    inject({
      crypto: ['crypto-browserify', 'default']
    }),
    nodePolyfills({
      include: ['crypto'],
      globals: {
        crypto: true
      }
    }),
    vue()
  ],
  resolve: {
    alias: {
      'crypto': 'crypto-browserify',
      '@': path.resolve(__dirname, 'src')
    }
  }
});
