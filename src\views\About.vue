<template>
  <div class="about">
    <div class="homeBgH">

      <span>档案数治管理平台</span>
      <div class="head-date">
        <div class="currentDate">
          <span>{{currentTime}}</span>
          <span>{{currentDate}}</span>
        </div>
        <div class="head-sel-arr">
          <div v-for="(item,index) in arr" :key="index" @click="handleClick(index)" :class="selNum==index?'selNum':'noSelnum'">{{ item }}</div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-left" style="width: 23.5%;">
        <head-component class="head-container" :selShow='false'>【{{selCity}}】档案类型占比</head-component>
        <pieLeft @typeName="handletypeName" ref="pieLeftInit"></pieLeft>
        <!-- <head-component class="head-container" :title="selCity">【{{selCity}}】{{typeName || '档案类型'}}占比情况</head-component> -->
        <head-component @changeSelNum="e=>selArr=e" class="head-container" @changeDate="handleChange" :title="'【'+selCity+'】'+(typeName || '档案类型')+'占比情况'">【{{selCity}}】{{typeName || '档案类型'}}占比</head-component>
        <barLineChart ref="barLineCharts" style="height: 300px;" :title="'【'+selCity+'】'+(typeName || '档案类型')+'占比情况'"></barLineChart>
        <head-component @changeSelNum="e=>selLinArr=e" :title="'【'+selCity+'】'+'利用情况'" @changeDate="handleChange1" class="head-container">【{{selCity}}】档案利用</head-component>
        <lineChart ref="lineCharts" style="height: 300px;" :title="'【'+selCity+'】'+'利用情况'"></lineChart>
      </div>

      <div class="content-center">
        <head-component class="head-container" :selShow='false'>【{{selCity}}】档案类型占比</head-component>
        <div class="introduce">
          <a-row :gutter="16" style="box-sizing: border-box;padding: 0 70px;" v-if="selCity=='上海市'">
            <a-col :span="8">
              <div class="introduce_total">
                <span>全{{selCity}}馆藏量</span>
                <!-- <span>1650</span> -->
                <CountTo :startVal="0" :endVal="collectionTotal.collectionNum" :duration="3000" :autoplay="true" :decimals="0" class="count-style" />
              </div>
            </a-col>
            <a-col :span="8">
              <div class="introduce_total">
                <span>当年全{{selCity}}利用人次</span>
                <CountTo :startVal="0" :endVal="collectionTotal.moonUseNum" :duration="3000" :autoplay="true" :decimals="0" class="count-style" />
                <!-- <span>1650</span> -->
              </div>
            </a-col>
            <a-col :span="8">
              <div class="introduce_total">
                <span>当月全{{selCity}}利用人次</span>
                <!-- <span>1650</span> -->
                <CountTo :startVal="0" :endVal="collectionTotal.useNum" :duration="3000" :autoplay="true" :decimals="0" class="count-style" />
              </div>
            </a-col>
          </a-row>
          <a-row v-else :gutter="16">
            <a-col :span="6">
              <div class="introduce_details">
                <span class="type">全上海馆藏量</span>
                <!-- <span class="city_total">1650</span> -->
                <CountTo :startVal="0" :endVal="collectionTotal.collectionNum" :duration="3000" :autoplay="true" :decimals="0" class="city_total" />
              </div>
            </a-col>
            <a-col :span="6">
              <div class="introduce_details">
                <span class="type">{{selCity}}馆藏量</span>
                <div style="display: flex;align-items: center;">
                  <!-- <span class="area-num">200</span> -->
                  <CountTo :startVal="0" :endVal="collectionTotal.areaCollectionNum" :duration="3000" :autoplay="true" :decimals="0" class="area-num" />
                  <span style="font-size: 14px;font-weight: 400;color: #BBCFE1;margin-left: 5px;display: block;">(</span>
                  <!-- <span class="up-down">12%</span> -->
                  <CountTo :startVal="0" :endVal="(collectionTotal.areaCollectionNum/collectionTotal.collectionNum*100) || 0" suffix="%" :duration="3000" :autoplay="true" :decimals="0" class="up-down" />
                  <span style="font-size: 14px;font-weight: 400;color: #BBCFE1;display: block;">)</span>
                </div>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="introduce_details">
                <span class="type">全年全上海利用人次</span>
                <div style="display: flex;align-items: center;">
                  <span class="date">当年:</span>
                  <!-- <span class="area-num">200</span> -->
                  <CountTo :startVal="0" :endVal="collectionTotal.useNum" :duration="3000" :autoplay="true" :decimals="0" class="area-num" />
                  <span style="font-size: 14px;font-weight: 400;color: #BBCFE1;margin-left: 5px;display: block;">(</span>
                  <span class="date">当月:</span>
                  <!-- <span class="up-down">36</span> -->
                  <CountTo :startVal="0" :endVal="collectionTotal.moonUseNum" :duration="3000" :autoplay="true" :decimals="0" class="up-down" />
                  <span style="font-size: 14px;font-weight: 400;color: #BBCFE1;display: block;">)</span>
                </div>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="introduce_details">
                <span class="type">{{selCity}}利用人次</span>
                <div style="display: flex;align-items: center;">
                  <span class="date">当年:</span>
                  <!-- <span class="area-num">200</span> -->
                  <CountTo :startVal="0" :endVal="collectionTotal.areaUseNum" :duration="3000" :autoplay="true" :decimals="0" class="area-num" />

                  <span style="font-size: 14px;font-weight: 400;color: #BBCFE1;margin-left: 5px;display: block;">(</span>
                  <span class="date">当月:</span>
                  <!-- <span class="up-down">36</span> -->
                  <CountTo :startVal="0" :endVal="collectionTotal.areaMoonUseNum" :duration="3000" :autoplay="true" :decimals="0" class="area-num" />

                  <span style="font-size: 14px;font-weight: 400;color: #BBCFE1;display: block;">)</span>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
        <div class="center-map">
          <!-- <shanghaiMap style="height: 570px;"></shanghaiMap> -->
          <testMap ref="testMapCharts" @changeStatus="e=>state.menuShow=e" @city="handleCity" :height="height"></testMap>
          <!-- <test></test> -->
          <div class="center-drordown" v-if="state.menuShow">
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleMenuClick">
                  <a-menu-item key="市局">
                    市局
                  </a-menu-item>
                  <a-menu-item key="机场分局">
                    机场分局
                  </a-menu-item>
                  <a-menu-item key="执法总队">
                    执法总队
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button style="color: rgba(150, 169, 186, 1);">{{state.menuValue}}<template #icon>
                  <!-- <UserOutlined /> -->
                </template>
              </a-button>
            </a-dropdown>
          </div>
        </div>
        <!-- <a-collapse v-model:activeKey="state.activeKey" :bordered="false">
          <a-collapse-panel key="1"> -->
        <img @click="handleShow" v-show="typeId=='1825725820232798212' || typeId=='1825725820232798210' " src="../assets/sx.png" style="width: 30px;height: 30px;z-index: 10000000;" alt="">
        <transition name="moveing">
          <div v-show="typeId=='1825725820232798210' && !isShow">
            <head-component class="head-container" @changeSelNum="e=>selCenterArr=e" @changeDate="handleChange2" :title="'【'+selCity+'】'+'】利用类型清单'" :selShow="true" :typeShow='false'>【{{selCity}}】利用清单</head-component>

            <a-table size="small" class="transparent-table" :pagination='false' :dataSource="state.dataSource" :scroll="{ x: 'max-content'}" :columns="state.columns1" bordered :rowClassName="() => { return 'rowClass' }">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key=='type'">
                  <span style="color:rgba(187, 207, 225, 1)">{{ record.type==1?'户次':'人次'}} </span>
                </template>
              </template>
            </a-table>
          </div>
        </transition>
        <transition name="moveing1">
          <div v-show="typeId=='1825725820232798212'&& !isShow">
            <head-component class="head-container" @changeSelNum="e=>selCenterArr=e" @changeDate="handleChange2" :title="'【'+selCity+'】'+'】利用类型清单'" :selShow="true" :typeShow='false'>【{{selCity}}】利用清单</head-component>

            <a-table size="small" class="transparent-table" :pagination='false' :dataSource="state.dataSource" :scroll="{ x: 'max-content'}" :columns="state.columns" bordered :rowClassName="() => { return 'rowClass' }">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key=='type'">
                  <span style="color:rgba(187, 207, 225, 1)">{{ record.type==1?'户次':'人次'}} </span>
                </template>
              </template>
            </a-table>
          </div>
        </transition>
        <!-- </a-collapse-panel>
        </a-collapse> -->
      </div>

      <div class="content-right">

        <head-component class="head-container" :selShow='false' :adminShow='true' title='摄像头管理' @handleSure="storeroom_camera">{{ cameraAddr }} </head-component>
        <a-carousel style="height: 290px;" :dots="false" autoplay :beforeChange="beforeChange">
          <div v-for="(item,index) in storeroom_camera_list" :key="index">
            <!-- <img style="width: 100%;height: 290px;" src="../assets//video.png" alt="">
             -->
            <!-- <video  ref="flvVideoPlayer" width="100%" height="290" controls></video> -->
            <!-- <flvVideo style="width: 100%;height: 290px;" :url='item.cameraRtsp'/> -->
            <!-- <NativeFlvPlayer
      :url="item.cameraRtsp"
      :isLive="true"
    /> -->
            <!-- </div> -->
            <iframe  style="width: 100%;height: 290px;padding: 10px 0;"  :src="'http://'+baseURL+':8908'+'/open/player?videoUrl='+item.cameraRtsp" frameborder="0"></iframe>

          </div>
        </a-carousel>
        <head-component class="head-container" :selShow='false'>{{ roomName || '-' }}</head-component>

        <a-carousel arrows style="height: 300px;" :after-change="onChange">
          <template #prevArrow>
            <div class="custom-slick-arrow" style="z-index: 10000;left: 40px;">
              <img src="../assets/selLeft.png" style="width: 40px;height: 40px;z-index: 1000;" alt="">
            </div>
          </template>
          <template #nextArrow>
            <div class="custom-slick-arrow" style="z-index: 10000;right: 40px;">
              <img src="../assets/selRight.png" style="width: 40px;height: 40px;z-index: 1000;" alt="">
            </div>
          </template>
          <div v-for="(item,index) in EnvironmentList" :key="index">
            <carouselChart :carouselData="item" ref="roomChartRef" style="height: 270px;"></carouselChart>
          </div>

        </a-carousel>

        <gridComponent :gridComponentData="gridComponentData"></gridComponent>

      </div>
    </div>
    <modals ref="modalSelComponents" :showType="false">
    </modals>
    <div class="footer"></div>

  </div>
</template>

<script setup lang="ts">
import HeadComponent from "../components/head.vue";
import pieLeft from "../components/pieLeft.vue";
import barLineChart from "../components/barLineChart.vue";
import lineChart from "../components/line.vue";
import carouselChart from "../components/carouselChart.vue";
import gridComponent from "../components/gridComponent.vue";
// import shanghaiMap from "../components/shanghaiMap.vue";
import testMap from "../components/testMap.vue";
// import testMap from "../components/testMap.vue";
import modals from "../components/modal.vue";
import * as api from "../api/index.ts";
import dayjs from "dayjs";
import request from "../utils/request";
import { CountTo } from "vue3-count-to";
import { ref, onMounted, reactive, nextTick,watch } from "vue";
import "animate.css";
const typeName = ref("");
const arr = ref(["九防系统", "智能库房系统"]);
const modalSelComponents = ref(null);
const isShow = ref(false);
watch(
  () => isShow.value,
  (newShow) => {
    if(!newShow){
      height.value='423'
    }else{
      height.value='700'
    }
  }
);
const state = reactive({
  menuShow: false,
  menuValue: "市局",
  dataSource: [],
  activeKey: "1",
  columns1: [
    {
      title: "利用类型",
      dataIndex: "firstName",
      key: "firstName",
      align: "center",
      colSpan: 2,
      customCell: (row, index) => {
        if (row.firstName == "调阅") {
          return { rowSpan: 3 };
        } else if (row.firstName == "借阅") {
          return { rowSpan: 2 };
        }
        return { rowSpan: 0 };
      },
    },
    {
      title: "",
      dataIndex: "classify",
      key: "classify ",
      align: "center",
      colSpan: 0,

      customCell: (row, index) => {
        if (row.classify == "人次") {
          return { rowSpan: 1 };
        } else if (row.classify == "调阅档案数量(卷)") {
          return { rowSpan: 1 };
        } else if (row.classify == "复制档案数量(页)") {
          return { rowSpan: 1 };
        } else if (row.classify == "借阅档案数量(卷)") {
          return { rowSpan: 1 };
        }
        return { rowSpan: 0 };
      },
    },
    {
      title: "办公室",
      dataIndex: "bgs",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "综合处",
      dataIndex: "zhc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "法规处",
      dataIndex: "fgc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "稽查处",
      dataIndex: "jcc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "登记处",
      dataIndex: "djc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "信用处",
      dataIndex: "xyc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "反垄断办",
      dataIndex: "fldb",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "竞争处",
      dataIndex: "jzc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "网监处",
      dataIndex: "wjc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "广告处",
      dataIndex: "ggc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "消保处",
      dataIndex: "xbc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "质量处",
      dataIndex: "zlc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "产品监督处",
      dataIndex: "cpjdc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "食品协调处",
      dataIndex: "spxtc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "食品生产处",
      dataIndex: "spscc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "食品经营处",
      dataIndex: "spjyc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "特食处",
      dataIndex: "tsc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "抽检处",
      dataIndex: "cjc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "特种处",
      dataIndex: "tzc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "计量处",
      dataIndex: "jlc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "标准技术处",
      dataIndex: "bzjsc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "标准创新处",
      dataIndex: "bzcxc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "认证处",
      dataIndex: "rzc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "认可处",
      dataIndex: "rkc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "宣传处",
      dataIndex: "xcc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "科财处",
      dataIndex: "kcc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "人事处",
      dataIndex: "rsc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "老干部处",
      dataIndex: "lgbc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "党建处",
      dataIndex: "djc1",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "科技和信息化处",
      dataIndex: "kjhxxc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "财务处",
      dataIndex: "cwc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "优化营商环境处",
      dataIndex: "yhyshjc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "反垄断办公室",
      dataIndex: "fldbgs",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "价格监督检查和反不正当竞争处",
      dataIndex: "jgjd",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "食品生产经营安全监督管理处",
      dataIndex: "spsc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "餐饮食品安全监督管理处",
      dataIndex: "cyspaq",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "认证认可监督管理处",
      dataIndex: "rzrk",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "基层指导处",
      dataIndex: "jczdc",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "机关党委",
      dataIndex: "jgdw",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "机关纪委",
      dataIndex: "jgjw",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
    {
      title: "市场监管学会",
      dataIndex: "scjdxh",
      align: "center",
      customRender: ({ text }) => text || "0",
    },
  ],
  columns: [
    {
      title: "设备类型",
      dataIndex: "type",
      key: "type",
      align: "center",
      children: [
        {
          title: "利用类型",
          dataIndex: "type",
          key: "type",
          width: 76,
          align: "center",
        },
      ],
    },
    {
      title: "移动端",
      dataIndex: "age",
      key: "age",
      align: "center",

      children: [
        {
          title: "电子营业执照小程序",
          dataIndex: "dzyzzzxcx",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "随身办企业云",
          dataIndex: "ssbqyy",
          key: "building",
          align: "center",
          width: 67.2,
        },
      ],
    },
    {
      title: "PC端",
      dataIndex: "address",
      key: "address",
      align: "center",

      children: [
        {
          title: "本企业",
          dataIndex: "bqy",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "律师事务所",
          dataIndex: "lssws",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "公、检、法、监察委",
          dataIndex: "gjfjcw",
          key: "building",
          align: "center",
          width: 79,
        },
        {
          title: "其他国家机构、社会组织",
          dataIndex: "qtgjjgshzzgw",
          key: "building",
          align: "center",
          width: 93,
        },
        {
          title: "全国律师",
          dataIndex: "qgls",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "公证人员",
          dataIndex: "gzry",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "破产管理人",
          dataIndex: "pcglr",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "授权密码",
          dataIndex: "sqmm",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "“白名单”授权",
          dataIndex: "bmdsq",
          key: "building",
          align: "center",
          width: 67.2,
        },
        {
          title: "窗口授权登录",
          dataIndex: "cksqdl",
          key: "building",
          align: "center",
          width: 67.2,
        },
      ],
    },
  ],
});

const selCity = ref("上海市");
const areaId = ref("");
const pieLeftInit = ref(null);
const selArr = ref(1);
const selLinArr = ref(1);
const selCenterArr = ref(1);
const lineCharts = ref(null);
const testMapCharts = ref(null);
const handleCity = (data?: any) => {
  globalCity(data.data.name);
};
const height = ref("573px");
const globalCity = (name?: any) => {
  selCity.value = name;
  if (
    typeId.value == "1825725820232798212" ||
    typeId.value == "1825725820232798210"
  ) {
    height.value = "423";
    // testMapCharts.value.style.height = "423px";
    isShow.value=false
  } else {
    // testMapCharts.value.style.height = "700px";
    height.value == "700";
    isShow.value=true
  }
  // testMapCharts.value.mapChartSize();

  areaId.value = area_tree.value.find(
    (item: any) => item.areaName == selCity.value
  )?.id;
  pieLeftInit.value.init(
    area_tree.value.find((item: any) => item.areaName == selCity.value)?.id
  );
  localStorage.setItem(
    "barlin",
    JSON.stringify({
      typeId: " ",
      areaId: areaId.value,
      dateType: selArr.value,
    })
  );
  barLineCharts.value.init(
    {
      typeId: " ",
      areaId: areaId.value,
      dateType: selArr.value,
    },
    false
  );
  localStorage.setItem(
    "lin",
    JSON.stringify({
      typeId: " ",
      areaId: areaId.value,
      dateType: selLinArr.value,
    })
  );
  lineCharts.value.init(
    {
      typeId: " ",
      areaId: areaId.value,
      dateType: selLinArr.value,
    },
    false
  );
  // archivelist()
  archivelist({ startTime: "", endTime: "" });
  initCenterdata();
};
const handleShow = () => {
  isShow.value = !isShow.value;
  // alert(isShow.value)
  console.log(22, isShow.value);
  
  // if (
  //   !isShow.value
  // ) {
  //     height.value = "423";
  // console.log(22, height.value);

  //     // testMapCharts.value.style.height = "423px";
  // } else {
  //     // testMapCharts.value.style.height = "700px";

  //     height.value = "700";
  // console.log(22, height.value);

  // }
};
const handleMenuClick = (e) => {
  state.menuValue = e.key;
  if (e.key == "市局") {
    testMapCharts.value.initMap(true);
  } else {
    testMapCharts.value.initMap(false);
  }
  globalCity(e.key);
};

//摄像头切换回调
const beforeChange = (index?: Number) => {
  cameraAddr.value = storeroom_camera_list.value[index]?.cameraName || "";
};
const selNum = ref(null);
const handleClick = (index?: Number) => {
  selNum.value = index;
  if (index == 1) {
    modalSelComponents.value.init();
    modalSelComponents.value.archivesList();
  } else {
    window.open("http://" + baseURL.value + ":8908/", "_blank");
  }
};
const currentTime = ref("");
const currentDate = ref("");
setInterval(() => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  const year = now.getFullYear(); //年
  const month = now.getMonth() + 1; //月
  const day = now.getDate(); //日
  currentTime.value = `${hours}:${minutes}:${seconds}`;
  currentDate.value = `${year}.${month}.${day}`;
}, 1000);
//查询摄像头列表
const storeroom_camera_list = ref([]);
const cameraAddr = ref("");
const videoUrl = ref(null);
const videoPlayer = ref(null);
const storeroom_camera = async () => {
  const res = await api.storeroom_camera();
  storeroom_camera_list.value = res?.data || [];

  // alert(request.defaults.baseURL+'open/player?videoUrl='+res?.data[0].cameraRtsp)
  cameraAddr.value = res?.data?.[0]?.cameraName || "";
  videoUrl.value = res?.data?.[0]?.cameraRtsp || "";
  // nextTick(()=>{
  //   testMapCharts.value.initMap()
  // })
};
const barLineCharts = ref(null);
const typeId = ref("");
// const
const handletypeName = (record?: any) => {
  typeName.value = record.typeName;
  typeId.value = record.typeId;
  // isShow.value=true
  if (
    typeId.value == "1825725820232798212" ||
    typeId.value == "1825725820232798210"
  ) {
    height.value = "423";
    isShow.value=false
    // testMapCharts.value.style.height = "423px";
  } else {
    height.value = "700";
    isShow.value=true
    // testMapCharts.value.style.height = "700px";
  }
  // testMapCharts.value.mapChartSize();

  // console.log(height.val);

  // if(record.typeId=='1825725820232798210')
  localStorage.setItem(
    "barlin",
    JSON.stringify({
      typeId: record.typeId,
      areaId: areaId.value,
      dateType: selArr.value,
    })
  );

  barLineCharts.value.init(
    {
      typeId: record.typeId,
      areaId: areaId.value,
      dateType: selLinArr.value,
    },
    false
  );
  localStorage.setItem(
    "lin",
    JSON.stringify({
      typeId: record.typeId,
      areaId: areaId.value,
      dateType: selArr.value,
    })
  );
  lineCharts.value.init(
    {
      typeId: record.typeId,
      areaId: areaId.value,
      dateType: selLinArr.value,
    },
    false
  );
  archivelist({ startTime: "", endTime: "" });
};

//树形区域
const area_tree = ref([]);
const collection_area_tree = async () => {
  const res = await api.collection_area_list();
  area_tree.value = res.data || [];
};

////别介意后续的朋友  难得去分类了

const handleChange = (record?: any) => {
  localStorage.setItem(
    "barlin",
    JSON.stringify({
      typeId: typeId.value,
      areaId: areaId.value,
      dateType: selArr.value,
      startTime: record[0],
      endTime: record[1],
    })
  );
  barLineCharts.value.init(
    {
      typeId: typeId.value,
      areaId: areaId.value,
      dateType: selArr.value,
      startTime: record[0],
      endTime: record[1],
    },
    false
  );
};
const handleChange1 = (record?: any) => {
  localStorage.setItem(
    "lin",
    JSON.stringify({
      typeId: typeId.value,
      areaId: areaId.value,
      dateType: selLinArr.value,
      startTime: record[0],
      endTime: record[1],
    })
  );
  lineCharts.value.init(
    {
      typeId: typeId.value,
      areaId: areaId.value,
      dateType: selLinArr.value,
      startTime: record[0],
      endTime: record[1],
    },
    false
  );
};
const handleChange2 = (record?: any) => {
  archivelist({ startTime: record[0], endTime: record[1] });
};
//获取利用类型表格数据
const archivelist = async (record?: any) => {
  const today = dayjs();
  const data = {
    areaId: areaId.value,
    typeId: typeId.value,
    dateType: selCenterArr.value,
  };
  if (selCenterArr.value == 1) {
    data.startTime =
      record.startTime || today.subtract(5, "year").format("YYYY");
    data.endTime = record.endTime || today.format("YYYY");
  } else if (selCenterArr.value == 2) {
    data.startTime =
      record.startTime || today.subtract(5, "year").format("YYYY-MM");
    data.endTime = record.endTime || today.format("YYYY-MM");
  } else {
    data.startTime =
      record.startTime || today.subtract(5, "year").format("YYYY-MM-DD");
    data.endTime = record.endTime || today.format("YYYY-MM-DD");
  }
  if (typeId.value != "1825725820232798210") {
    const res = await api.archives_use_list(data);
    state.dataSource = [
      {
        type: "1",
        dzyzzzxcx: "",
        ssbqyy: "",
        bqy: "",
        lssws: "",
        gjfjcw: "",
        qtgjjgshzzgw: "",
        qgls: "",
        gzry: "",
        pcglr: "",
        sqmm: "",
        bmdsq: "",
        cksqdl: "",
      },
      {
        type: "2",
        dzyzzzxcx: "",
        ssbqyy: "",
        bqy: "",
        lssws: "",
        gjfjcw: "",
        qtgjjgshzzgw: "",
        qgls: "",
        gzry: "",
        pcglr: "",
        sqmm: "",
        bmdsq: "",
        cksqdl: "",
      },
    ];
    res?.data.forEach((item) => {
      // if (item.deviceType == "移动端") {
      if (item.typeName == "电子营业执照小程序") {
        state.dataSource[0].dzyzzzxcx = item.householdNum;
        state.dataSource[1].dzyzzzxcx = item.peopleNum;
      } else if (item.typeName == "随身办企业云") {
        state.dataSource[0].ssbqyy = item.householdNum;
        state.dataSource[1].ssbqyy = item.peopleNum;
      } else if (item.typeName == "本企业") {
        state.dataSource[0].bqy = item.householdNum;
        state.dataSource[1].bqy = item.peopleNum;
      } else if (item.typeName == "律师事务所") {
        state.dataSource[0].lssws = item.householdNum;
        state.dataSource[1].lssws = item.peopleNum;
      } else if (item.typeName == "公、检、法、监察委") {
        state.dataSource[0].gjfjcw = item.householdNum;
        state.dataSource[1].gjfjcw = item.peopleNum;
      } else if (item.typeName == "其他国家机构、社会组织公务") {
        state.dataSource[0].qtgjjgshzzgw = item.householdNum;
        state.dataSource[1].qtgjjgshzzgw = item.peopleNum;
      } else if (item.typeName == "全国律师") {
        state.dataSource[0].qgls = item.householdNum;
        state.dataSource[1].qgls = item.peopleNum;
      } else if (item.typeName == "公证人员") {
        state.dataSource[0].gzry = item.householdNum;
        state.dataSource[1].gzry = item.peopleNum;
      } else if (item.typeName == "破产管理人") {
        state.dataSource[0].pcglr = item.householdNum;
        state.dataSource[1].pcglr = item.peopleNum;
      } else if (item.typeName == "授权密码") {
        state.dataSource[0].sqmm = item.householdNum;
        state.dataSource[1].sqmm = item.peopleNum;
      } else if (item.typeName == "“白名单”授权") {
        state.dataSource[0].bmdsq = item.householdNum;
        state.dataSource[1].bmdsq = item.peopleNum;
      } else if (item.typeName == "窗口授权登录") {
        state.dataSource[0].cksqdl = item.householdNum;
        state.dataSource[1].cksqdl = item.peopleNum;
      }
    });
  } else {
    state.dataSource = [
      {
        firstName: "调阅",
        classify: "人次",
        bgs: "",
        zhc: "",
        fgc: "",
        jcc: "",
        djc: "",
        xyc: "",
        fldb: "",
        jzc: "",
        wjc: "",
        ggc: "",
        xbc: "",
        zlc: "",
        cpjdc: "",
        xpxtc: "",
        xpscc: "",
        xpjyc: "",
        tsc: "",
        cjc: "",
        tzc: "",
        jlc: "",
        bzjsc: "",
        bzcxc: "",
        rzc: "",
        rkc: "",
        xcc: "",
        kcc: "",
        rsc: "",
        lgbc: "",
        djc1: "",
        kjhxxc: "",
        cwc: "",
        yhyshjc: "",
        fldbgs: "",
        jgjd: "",
        spsc: "",
        cyspaq: "",
        rzrk: "",
        jczdc: "",
        jgdw: "",
        jgjw: "",
        scjdxh: "",
      },
      {
        // type: "1",
        firstName: "",

        classify: "调阅档案数量(卷)",
        bgs: "",
        zhc: "",
        fgc: "",
        jcc: "",
        djc: "",
        xyc: "",
        fldb: "",
        jzc: "",
        wjc: "",
        ggc: "",
        xbc: "",
        zlc: "",
        cpjdc: "",
        xpxtc: "",
        xpscc: "",
        xpjyc: "",
        tsc: "",
        cjc: "",
        tzc: "",
        jlc: "",
        bzjsc: "",
        bzcxc: "",
        rzc: "",
        rkc: "",
        xcc: "",
        kcc: "",
        rsc: "",
        lgbc: "",
        djc1: "",
        kjhxxc: "",
        cwc: "",
        yhyshjc: "",
        fldbgs: "",
        jgjd: "",
        spsc: "",
        cyspaq: "",
        rzrk: "",
        jczdc: "",
        jgdw: "",
        jgjw: "",
        scjdxh: "",
      },
      {
        // type: "1",
        firstName: "",

        classify: "复制档案数量(页)",
        bgs: "",
        zhc: "",
        fgc: "",
        jcc: "",
        djc: "",
        xyc: "",
        fldb: "",
        jzc: "",
        wjc: "",
        ggc: "",
        xbc: "",
        zlc: "",
        cpjdc: "",
        xpxtc: "",
        xpscc: "",
        xpjyc: "",
        tsc: "",
        cjc: "",
        tzc: "",
        jlc: "",
        bzjsc: "",
        bzcxc: "",
        rzc: "",
        rkc: "",
        xcc: "",
        kcc: "",
        rsc: "",
        lgbc: "",
        djc1: "",
        kjhxxc: "",
        cwc: "",
        yhyshjc: "",
        fldbgs: "",
        jgjd: "",
        spsc: "",
        cyspaq: "",
        rzrk: "",
        jczdc: "",
        jgdw: "",
        jgjw: "",
        scjdxh: "",
      },
      {
        classify: "人次",
        firstName: "借阅",
        bgs: "",
        zhc: "",
        fgc: "",
        jcc: "",
        djc: "",
        xyc: "",
        fldb: "",
        jzc: "",
        wjc: "",
        ggc: "",
        xbc: "",
        zlc: "",
        cpjdc: "",
        xpxtc: "",
        xpscc: "",
        xpjyc: "",
        tsc: "",
        cjc: "",
        tzc: "",
        jlc: "",
        bzjsc: "",
        bzcxc: "",
        rzc: "",
        rkc: "",
        xcc: "",
        kcc: "",
        rsc: "",
        lgbc: "",
        djc1: "",
        kjhxxc: "",
        cwc: "",
        yhyshjc: "",
        fldbgs: "",
        jgjd: "",
        spsc: "",
        cyspaq: "",
        rzrk: "",
        jczdc: "",
        jgdw: "",
        jgjw: "",
        scjdxh: "",
      },
      {
        // type: "2",
        firstName: "",
        classify: "借阅档案数量(卷)",
        bgs: "",
        zhc: "",
        fgc: "",
        jcc: "",
        djc: "",
        xyc: "",
        fldb: "",
        jzc: "",
        wjc: "",
        ggc: "",
        xbc: "",
        zlc: "",
        cpjdc: "",
        xpxtc: "",
        xpscc: "",
        xpjyc: "",
        tsc: "",
        cjc: "",
        tzc: "",
        jlc: "",
        bzjsc: "",
        bzcxc: "",
        rzc: "",
        rkc: "",
        xcc: "",
        kcc: "",
        rsc: "",
        lgbc: "",
        djc1: "",
        kjhxxc: "",
        cwc: "",
        yhyshjc: "",
        fldbgs: "",
        jgjd: "",
        spsc: "",
        cyspaq: "",
        rzrk: "",
        jczdc: "",
        jgdw: "",
        jgjw: "",
        scjdxh: "",
      },
    ];
    const res = await api.archives_use_document_list(data);
    // state.dataSource=res.data || []
    res?.data.forEach((item) => {
      if (item.typeName == "办公室") {
        state.dataSource[0].bgs = item.transferPeopleNum;
        state.dataSource[1].bgs = item.transferDossierNum;
        state.dataSource[2].bgs = item.transferPageNum;
        state.dataSource[3].bgs = item.borrowPeopleNum;
        state.dataSource[4].bgs = item.borrowDossierNum;
      } else if (item.typeName == "综合处") {
        state.dataSource[0].zhc = item.transferPeopleNum;
        state.dataSource[1].zhc = item.transferDossierNum;
        state.dataSource[2].zhc = item.transferPageNum;
        state.dataSource[3].zhc = item.borrowPeopleNum;
        state.dataSource[4].zhc = item.borrowDossierNum;
      } else if (item.typeName == "法规处") {
        state.dataSource[0].fgc = item.transferPeopleNum;
        state.dataSource[1].fgc = item.transferDossierNum;
        state.dataSource[2].fgc = item.transferPageNum;
        state.dataSource[3].fgc = item.borrowPeopleNum;
        state.dataSource[4].fgc = item.borrowDossierNum;
      } else if (item.typeName == "稽查处") {
        state.dataSource[0].jcc = item.transferPeopleNum;
        state.dataSource[1].jcc = item.transferDossierNum;
        state.dataSource[2].jcc = item.transferPageNum;
        state.dataSource[3].jcc = item.borrowPeopleNum;
        state.dataSource[4].jcc = item.borrowDossierNum;
      } else if (item.typeName == "登记处") {
        state.dataSource[0].djc = item.transferPeopleNum;
        state.dataSource[1].djc = item.transferDossierNum;
        state.dataSource[2].djc = item.transferPageNum;
        state.dataSource[3].djc = item.borrowPeopleNum;
        state.dataSource[4].djc = item.borrowDossierNum;
      } else if (item.typeName == "信用处") {
        state.dataSource[0].xyc = item.transferPeopleNum;
        state.dataSource[1].xyc = item.transferDossierNum;
        state.dataSource[2].xyc = item.transferPageNum;
        state.dataSource[3].xyc = item.borrowPeopleNum;
        state.dataSource[4].xyc = item.borrowDossierNum;
      } else if (item.typeName == "反垄断办") {
        state.dataSource[0].fldb = item.transferPeopleNum;
        state.dataSource[1].fldb = item.transferDossierNum;
        state.dataSource[2].fldb = item.transferPageNum;
        state.dataSource[3].fldb = item.borrowPeopleNum;
        state.dataSource[4].fldb = item.borrowDossierNum;
      } else if (item.typeName == "竞争处") {
        state.dataSource[0].jzc = item.transferPeopleNum;
        state.dataSource[1].jzc = item.transferDossierNum;
        state.dataSource[2].jzc = item.transferPageNum;
        state.dataSource[3].jzc = item.borrowPeopleNum;
        state.dataSource[4].jzc = item.borrowDossierNum;
      } else if (item.typeName == "网监处") {
        state.dataSource[0].wjc = item.transferPeopleNum;
        state.dataSource[1].wjc = item.transferDossierNum;
        state.dataSource[2].wjc = item.transferPageNum;
        state.dataSource[3].wjc = item.borrowPeopleNum;
        state.dataSource[4].wjc = item.borrowDossierNum;
      } else if (item.typeName == "广告处") {
        state.dataSource[0].ggc = item.transferPeopleNum;
        state.dataSource[1].ggc = item.transferDossierNum;
        state.dataSource[2].ggc = item.transferPageNum;
        state.dataSource[3].ggc = item.borrowPeopleNum;
        state.dataSource[4].ggc = item.borrowDossierNum;
      } else if (item.typeName == "消保处") {
        state.dataSource[0].xbc = item.transferPeopleNum;
        state.dataSource[1].xbc = item.transferDossierNum;
        state.dataSource[2].xbc = item.transferPageNum;
        state.dataSource[3].xbc = item.borrowPeopleNum;
        state.dataSource[4].xbc = item.borrowDossierNum;
      } else if (item.typeName == "质量处") {
        state.dataSource[0].zlc = item.transferPeopleNum;
        state.dataSource[1].zlc = item.transferDossierNum;
        state.dataSource[2].zlc = item.transferPageNum;
        state.dataSource[3].zlc = item.borrowPeopleNum;
        state.dataSource[4].zlc = item.borrowDossierNum;
      } else if (item.typeName == "产品监督处") {
        state.dataSource[0].cpjdc = item.transferPeopleNum;
        state.dataSource[1].cpjdc = item.transferDossierNum;
        state.dataSource[2].cpjdc = item.transferPageNum;
        state.dataSource[3].cpjdc = item.borrowPeopleNum;
        state.dataSource[4].cpjdc = item.borrowDossierNum;
      } else if (item.typeName == "食品协调处") {
        state.dataSource[0].spxtc = item.transferPeopleNum;
        state.dataSource[1].spxtc = item.transferDossierNum;
        state.dataSource[2].spxtc = item.transferPageNum;
        state.dataSource[3].spxtc = item.borrowPeopleNum;
        state.dataSource[4].spxtc = item.borrowDossierNum;
      } else if (item.typeName == "食品生产处") {
        state.dataSource[0].spscc = item.transferPeopleNum;
        state.dataSource[1].spscc = item.transferDossierNum;
        state.dataSource[2].spscc = item.transferPageNum;
        state.dataSource[3].spscc = item.borrowPeopleNum;
        state.dataSource[4].spscc = item.borrowDossierNum;
      } else if (item.typeName == "食品经营处") {
        state.dataSource[0].spjyc = item.transferPeopleNum;
        state.dataSource[1].spjyc = item.transferDossierNum;
        state.dataSource[2].spjyc = item.transferPageNum;
        state.dataSource[3].spjyc = item.borrowPeopleNum;
        state.dataSource[4].spjyc = item.borrowDossierNum;
      } else if (item.typeName == "特食处") {
        state.dataSource[0].tsc = item.transferPeopleNum;
        state.dataSource[1].tsc = item.transferDossierNum;
        state.dataSource[2].tsc = item.transferPageNum;
        state.dataSource[3].tsc = item.borrowPeopleNum;
        state.dataSource[4].tsc = item.borrowDossierNum;
      } else if (item.typeName == "抽检处") {
        state.dataSource[0].cjc = item.transferPeopleNum;
        state.dataSource[1].cjc = item.transferDossierNum;
        state.dataSource[2].cjc = item.transferPageNum;
        state.dataSource[3].cjc = item.borrowPeopleNum;
        state.dataSource[4].cjc = item.borrowDossierNum;
      } else if (item.typeName == "特种处") {
        state.dataSource[0].tzc = item.transferPeopleNum;
        state.dataSource[1].tzc = item.transferDossierNum;
        state.dataSource[2].tzc = item.transferPageNum;
        state.dataSource[3].tzc = item.borrowPeopleNum;
        state.dataSource[4].tzc = item.borrowDossierNum;
      } else if (item.typeName == "计量处") {
        state.dataSource[0].jlc = item.transferPeopleNum;
        state.dataSource[1].jlc = item.transferDossierNum;
        state.dataSource[2].jlc = item.transferPageNum;
        state.dataSource[3].jlc = item.borrowPeopleNum;
        state.dataSource[4].jlc = item.borrowDossierNum;
      } else if (item.typeName == "标准技术处") {
        state.dataSource[0].bzjsc = item.transferPeopleNum;
        state.dataSource[1].bzjsc = item.transferDossierNum;
        state.dataSource[2].bzjsc = item.transferPageNum;
        state.dataSource[3].bzjsc = item.borrowPeopleNum;
        state.dataSource[4].bzjsc = item.borrowDossierNum;
      } else if (item.typeName == "标准创新处") {
        state.dataSource[0].bzcxc = item.transferPeopleNum;
        state.dataSource[1].bzcxc = item.transferDossierNum;
        state.dataSource[2].bzcxc = item.transferPageNum;
        state.dataSource[3].bzcxc = item.borrowPeopleNum;
        state.dataSource[4].bzcxc = item.borrowDossierNum;
      } else if (item.typeName == "认证处") {
        state.dataSource[0].rzc = item.transferPeopleNum;
        state.dataSource[1].rzc = item.transferDossierNum;
        state.dataSource[2].rzc = item.transferPageNum;
        state.dataSource[3].rzc = item.borrowPeopleNum;
        state.dataSource[4].rzc = item.borrowDossierNum;
      } else if (item.typeName == "认可处") {
        state.dataSource[0].rkc = item.transferPeopleNum;
        state.dataSource[1].rkc = item.transferDossierNum;
        state.dataSource[2].rkc = item.transferPageNum;
        state.dataSource[3].rkc = item.borrowPeopleNum;
        state.dataSource[4].rkc = item.borrowDossierNum;
      } else if (item.typeName == "宣传处") {
        state.dataSource[0].xcc = item.transferPeopleNum;
        state.dataSource[1].xcc = item.transferDossierNum;
        state.dataSource[2].xcc = item.transferPageNum;
        state.dataSource[3].xcc = item.borrowPeopleNum;
        state.dataSource[4].xcc = item.borrowDossierNum;
      } else if (item.typeName == "科财处") {
        state.dataSource[0].kcc = item.transferPeopleNum;
        state.dataSource[1].kcc = item.transferDossierNum;
        state.dataSource[2].kcc = item.transferPageNum;
        state.dataSource[3].kcc = item.borrowPeopleNum;
        state.dataSource[4].kcc = item.borrowDossierNum;
      } else if (item.typeName == "人事处") {
        state.dataSource[0].rsc = item.transferPeopleNum;
        state.dataSource[1].rsc = item.transferDossierNum;
        state.dataSource[2].rsc = item.transferPageNum;
        state.dataSource[3].rsc = item.borrowPeopleNum;
        state.dataSource[4].rsc = item.borrowDossierNum;
      } else if (item.typeName == "老干部处") {
        state.dataSource[0].lgbc = item.transferPeopleNum;
        state.dataSource[1].lgbc = item.transferDossierNum;
        state.dataSource[2].lgbc = item.transferPageNum;
        state.dataSource[3].lgbc = item.borrowPeopleNum;
        state.dataSource[4].lgbc = item.borrowDossierNum;
      } else if (item.typeName == "党建处") {
        state.dataSource[0].djc1 = item.transferPeopleNum;
        state.dataSource[1].djc1 = item.transferDossierNum;
        state.dataSource[2].djc1 = item.transferPageNum;
        state.dataSource[3].djc1 = item.borrowPeopleNum;
        state.dataSource[4].djc1 = item.borrowDossierNum;
      } else if (item.typeName == "科技和信息化处") {
        state.dataSource[0].kjhxxc = item.transferPeopleNum;
        state.dataSource[1].kjhxxc = item.transferDossierNum;
        state.dataSource[2].kjhxxc = item.transferPageNum;
        state.dataSource[3].kjhxxc = item.borrowPeopleNum;
        state.dataSource[4].kjhxxc = item.borrowDossierNum;
      } else if (item.typeName == "财务处") {
        state.dataSource[0].cwc = item.transferPeopleNum;
        state.dataSource[1].cwc = item.transferDossierNum;
        state.dataSource[2].cwc = item.transferPageNum;
        state.dataSource[3].cwc = item.borrowPeopleNum;
        state.dataSource[4].cwc = item.borrowDossierNum;
      } else if (item.typeName == "优化营商环境处") {
        state.dataSource[0].yhyshjc = item.transferPeopleNum;
        state.dataSource[1].yhyshjc = item.transferDossierNum;
        state.dataSource[2].yhyshjc = item.transferPageNum;
        state.dataSource[3].yhyshjc = item.borrowPeopleNum;
        state.dataSource[4].yhyshjc = item.borrowDossierNum;
      } else if (item.typeName == "反垄断办公室") {
        state.dataSource[0].fldbgs = item.transferPeopleNum;
        state.dataSource[1].fldbgs = item.transferDossierNum;
        state.dataSource[2].fldbgs = item.transferPageNum;
        state.dataSource[3].fldbgs = item.borrowPeopleNum;
        state.dataSource[4].fldbgs = item.borrowDossierNum;
      } else if (item.typeName == "价格监督检查和反不正当竞争处") {
        state.dataSource[0].jgjd = item.transferPeopleNum;
        state.dataSource[1].jgjd = item.transferDossierNum;
        state.dataSource[2].jgjd = item.transferPageNum;
        state.dataSource[3].jgjd = item.borrowPeopleNum;
        state.dataSource[4].jgjd = item.borrowDossierNum;
      } else if (item.typeName == "食品生产经营安全监督管理处") {
        state.dataSource[0].spsc = item.transferPeopleNum;
        state.dataSource[1].spsc = item.transferDossierNum;
        state.dataSource[2].spsc = item.transferPageNum;
        state.dataSource[3].spsc = item.borrowPeopleNum;
        state.dataSource[4].spsc = item.borrowDossierNum;
      } else if (item.typeName == "餐饮食品安全监督管理处") {
        state.dataSource[0].cyspaq = item.transferPeopleNum;
        state.dataSource[1].cyspaq = item.transferDossierNum;
        state.dataSource[2].cyspaq = item.transferPageNum;
        state.dataSource[3].cyspaq = item.borrowPeopleNum;
        state.dataSource[4].cyspaq = item.borrowDossierNum;
      } else if (item.typeName == "认证认可监督管理处") {
        state.dataSource[0].rzrk = item.transferPeopleNum;
        state.dataSource[1].rzrk = item.transferDossierNum;
        state.dataSource[2].rzrk = item.transferPageNum;
        state.dataSource[3].rzrk = item.borrowPeopleNum;
        state.dataSource[4].rzrk = item.borrowDossierNum;
      } else if (item.typeName == "基层指导处") {
        state.dataSource[0].jczdc = item.transferPeopleNum;
        state.dataSource[1].jczdc = item.transferDossierNum;
        state.dataSource[2].jczdc = item.transferPageNum;
        state.dataSource[3].jczdc = item.borrowPeopleNum;
        state.dataSource[4].jczdc = item.borrowDossierNum;
      } else if (item.typeName == "机关党委") {
        state.dataSource[0].jgdw = item.transferPeopleNum;
        state.dataSource[1].jgdw = item.transferDossierNum;
        state.dataSource[2].jgdw = item.transferPageNum;
        state.dataSource[3].jgdw = item.borrowPeopleNum;
        state.dataSource[4].jgdw = item.borrowDossierNum;
      } else if (item.typeName == "机关纪委") {
        state.dataSource[0].jgjw = item.transferPeopleNum;
        state.dataSource[1].jgjw = item.transferDossierNum;
        state.dataSource[2].jgjw = item.transferPageNum;
        state.dataSource[3].jgjw = item.borrowPeopleNum;
        state.dataSource[4].jgjw = item.borrowDossierNum;
      } else if (item.typeName == "市场监管学会") {
        state.dataSource[0].scjdxh = item.transferPeopleNum;
        state.dataSource[1].scjdxh = item.transferDossierNum;
        state.dataSource[2].scjdxh = item.transferPageNum;
        state.dataSource[3].scjdxh = item.borrowPeopleNum;
        state.dataSource[4].scjdxh = item.borrowDossierNum;
      }
    });
  }
};
//获取典藏数据
const collectionTotal = ref({});
const initCenterdata = async () => {
  const { data } = await api.collection_total({ areaId: areaId.value });
  collectionTotal.value = data || {};
};
//获取健康数据
const EnvironmentList = ref([]);
const roomName = ref();
const gridComponentData = ref({});
const storeroomEnvironment = async () => {
  const { data } = await api.storeroom_environment();
  EnvironmentList.value = data || [];
  if (data && data.length > 0) {
    roomName.value = data[0].roomName;
    gridComponentData.value = data[0];
  }
};
const onChange = (e?: any) => {
  console.log(e);
  roomName.value = EnvironmentList.value[e].roomName;
  gridComponentData.value = EnvironmentList.value[e];
};
const baseURL = ref(null);
onMounted(() => {
  if (sessionStorage.getItem("baseUrl")) {
    baseURL.value = sessionStorage.getItem("baseUrl");
  } else {
    baseURL.value = "**************";
  }
  console.log(baseURL.value, "自动获取的baseUrl");

  // sessionStorage.setItem('baseUrl',window.location.hostname)
  // alert(window.location.hostname)
  archivelist({ startTime: "", endTime: "" });
  storeroom_camera();
  collection_area_tree();
  initCenterdata();
  storeroomEnvironment();
});
</script>
<style scoped>
/* .rowClass>th {
  border-right: 1px solid #ABD1F5 !important;
  border-bottom: 1px solid #ABD1F5 !important;
 
}


.ant-dropdown .ant-dropdown-menu {
  background: #0c3850 !important;
  padding: 0;
}
.ant-dropdown-menu-title-content {
  color: rgba(150, 169, 186, 1);
} */
</style>
<style scoped lang="scss">
:deep(.ant-table-thead > tr > th) {
  border: 1px solid rgba(255, 255, 255, 0.1) !important; /* 修改内边框 */
}
:deep(.transparent-table .ant-table) {
  border: none;
  background: transparent !important;
  /* border: 1px solid rgba(255, 255, 255, 0.1); */
}
// :deep(:where(.css-dev-only-do-not-override-1p3hq3p).ant-dropdown-menu-submenu .ant-dropdown-menu){
//   background: #0C3850 !important;
//   padding: 0;
// }

.center-map {
  position: relative;
  .center-drordown {
    position: absolute;
    right: 0;
    top: -50px;
  }
  :deep(.ant-btn-default) {
    width: 120px;
    height: 40px;
    background: rgba(32, 98, 131, 0.3);
    border-color: rgba(99, 216, 255, 0.2);
  }
}
:deep(.ant-carousel .slick-dots-bottom) {
  bottom: -22px !important;
  button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}
:deep(.ant-carousel .slick-dots li) {
  width: 10px;
}
:deep(.ant-carousel .slick-dots .slick-active button) {
  background: #07bdfa;
}
.about {
  width: 100%;
  height: 100%;
  background: url("/src/assets/indexBg.png") no-repeat;
  background-size: 100% 100%;
  z-index: 10;
  .footer {
    width: 100%;
    height: 37px;
    background: url("/src/assets/footer.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    bottom: 0;
  }
  .content {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 20px;
    .contenr-left {
      width: 23%;
    }
    .transparent-table .ant-table {
      background: transparent;
    }
    .transparent-table .ant-table table {
      background: transparent;
    }
    :deep(
        .ant-table-wrapper
          .ant-table.ant-table-bordered
          > .ant-table-container
          > .ant-table-content
          > table
      ) {
      border: none;
    }
    :deep(
        .ant-table-wrapper
          .ant-table.ant-table-bordered
          > .ant-table-container
          > .ant-table-content
          > table
          > thead
          > tr:not(:last-child)
          > th
      ) {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    :deep(
        .ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container
      ) {
      border-inline-start: 1px solid rgba(255, 255, 255, 0.1);
    }
    // :deep(.ant-table-wrapper .ant-table.ant-table-bordered >.ant-table-container >.ant-table-content >table >thead >tr:not(:last-child)>th){
    //   border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    // }
    :deep(
        .ant-table-wrapper
          .ant-table.ant-table-bordered
          > .ant-table-container
          > .ant-table-content
          > table
          > thead
          > tr
          > th
      ) {
      border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
    }
    :deep(
        .ant-table-wrapper
          .ant-table.ant-table-bordered
          .ant-table-tbody
          > tr
          > td
      ) {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    :deep(.ant-table-tbody) {
      > tr:hover:not(.ant-table-expanded-row) > td,
      .ant-table-row-hover,
      .ant-table-row-hover > td {
        color: #ffffff;
        background: none;
      }
    }
    :deep(.ant-table-cell) {
      color: rgba(150, 169, 186, 1);
      padding: 0;
    }
    :deep(.ant-table-thead > tr > th) {
      color: rgba(187, 207, 225, 1);
      background-color: transparent !important;
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      padding: 7px;
    }
    :deep(
        .ant-table-wrapper
          .ant-table.ant-table-bordered
          > .ant-table-container
          > .ant-table-content
          > table
          > tbody
          > tr
          > td
      ) {
      padding: 6px;
      border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr) {
      &:nth-child(2) {
        th {
          &:first-child {
            color: rgba(187, 207, 225, 1);
          }
          color: rgba(150, 169, 186, 1);
          font-size: 14px;
        }
      }
      // border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .content-center {
      width: 53%;
      // flex: 1;
      // margin: 0 20px;
      .introduce {
        background: url("../assets/introduce.png") no-repeat;
        background-size: 100% 100%;
        width: 100%;
        height: 80px;
        box-sizing: border-box;
        // padding: 0 70px;
        margin-bottom: 20px;
        .introduce_total {
          height: 80px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          span {
            display: block;
            &:first-child {
              font-weight: 400;
              font-style: Regular;
              font-size: 14px;
              color: #bbcfe1;
              // margin-bottom: 10px;
            }
            &:last-child {
              font-weight: 700;
              font-style: Bold;
              font-size: 30px;
              color: #07bdfa;
            }
          }
        }
        .introduce_details {
          height: 80px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .type {
            font-weight: 400;
            font-style: Regular;
            font-size: 14px;
            color: #bbcfe1;
          }
          .city_total {
            font-weight: 700;
            font-style: Bold;
            font-size: 30px;
            color: #07bdfa;
          }
          .area-num {
            font-weight: 700;
            font-style: Bold;
            font-size: 24px;
            color: #07bdfa;
          }
          .up-down {
            font-weight: 700;
            font-style: Bold;
            font-size: 24px;
            color: #fbe94b;
          }
          .date {
            font-weight: 400;
            font-style: Regular;
            font-size: 14px;
            color: #bbcfe1;
          }
        }
      }
    }

    .content-right {
      width: 23%;
    }
  }
  .homeBgH {
    position: relative;
    display: flex;
    justify-content: center;
    // justify-content: space-between;
    // align-items: center;
    width: 100%;
    height: 110px;
    background: url("/src/assets/homeBgH.png") no-repeat;
    background-size: 100% 100%;
    // z-index: 1000;
    span {
      // background: linear-gradient(180deg, #C4D7E6 0%, #FFFFFF 100%);
      font-weight: 700;
      font-style: Bold;
      font-size: 38px;
      leading-trim: NONE;
      line-height: 100%;
      letter-spacing: 8%;
      line-height: 110px;
      color: #ffffff;
      // text-align: center;
    }
    .head-date {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .currentDate {
        display: flex;
        align-items: center;
        margin-left: 60px;
        span {
          display: block;
          &:first-child {
            font-weight: 700;
            font-style: Bold;
            font-size: 24px;
            color: #bbcfe1;
            margin-right: 20px;
          }
          &:last-child {
            font-weight: 700;
            font-style: Bold;
            font-size: 14px;
            color: #96a9ba;
          }
        }
      }
      .head-sel-arr {
        display: flex;
        align-items: center;
        position: relative;
        margin-right: 60px;
        .selNum,
        .noSelnum {
          font-weight: 700;
          font-style: Bold;
          font-size: 18px;
          text-align: center;
          background: linear-gradient(180deg, #42acd7 0%, #d3e9f3 100%);
          /* 将文本透明度设置为0，以便背景渐变可见 */
          color: transparent;
          /* 使用背景渐变来填充文本背景 */
          -webkit-background-clip: text;
          background-clip: text;
          margin: 0 10px;
          cursor: pointer;
        }
        .selNum {
          color: #ffffff;
          position: relative;
          &::after {
            content: " ";
            width: 60px;
            height: 24px;
            background: url("/src/assets/frame.png") no-repeat;
            background-size: 100% 100%;
            position: absolute;
            left: 15%;
            bottom: -8px;
          }
        }
      }
    }
  }
}

.head-container {
  width: 440px;
}
:deep(.slick-arrow.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  transition: ease all 0.3s;
  z-index: 1;
  img {
    width: 25px;
    height: 25px;
  }
}
.moveing-enter-active {
    animation: slide-in 1.5s ease;
}

.moveing-leave-active {
    animation: slide-out 0s ease;
}
@keyframes slide-in {
    0% {
        transform: translateY(100px);
    }

    100% {
        transform: translateY(0);
    }
}

@keyframes slide-out {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(100px);
    }
}
.moveing1-enter-active {
    animation: slide1-in 1.5s ease;
}

.moveing1-leave-active {
    animation: slide1-out 0s ease;
}
@keyframes slide1-in {
    0% {
        transform: translateY(100px);
    }

    100% {
        transform: translateY(0);
    }
}

@keyframes slide1-out {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(100px);
    }
}
</style>