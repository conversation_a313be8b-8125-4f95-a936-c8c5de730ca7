<template>
  <div>
    <a-row :gutter="16">
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel">{{ props.gridComponentData.co2_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="０．０１mg/m３" arrow-point-at-center>
        <span class="dataType">二氧化碳</span>
        </a-tooltip>
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel">{{ props.gridComponentData.hcho_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="０．１０mg/m３" arrow-point-at-center>
        <span class="dataType">甲醛值</span>
        </a-tooltip>
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel" >{{ props.gridComponentData.hum_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="４５％~６０％" arrow-point-at-center>

        <span class="dataType">湿度</span>
        </a-tooltip>
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel" >{{ props.gridComponentData.ozone_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="０．０１mg/m３" arrow-point-at-center>

        <span class="dataType">臭氧</span>
        </a-tooltip>
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel">{{ props.gridComponentData.pm10_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="０．１５mg/m３" arrow-point-at-center>

        <span class="dataType">PM10</span>
        </a-tooltip>
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel">{{ props.gridComponentData.pm25_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="７５μg/m３" arrow-point-at-center>

        <span class="dataType">PM2.5</span>
        </a-tooltip>
      </div>
    </a-col>
    <!-- <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel">{{ props.gridComponentData.score_value|| "-" }}</span>
        <span class="dataType">得分状态{{ props.gridComponentData.score_state|| "-" }}</span>
      </div>
    </a-col> -->
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel">{{ props.gridComponentData.smoke_state}}</span>
        <!-- <a-tooltip placement="topLeft" title="Prompt Text" arrow-point-at-center> -->

        <span class="dataType">烟雾变送器</span>
        <!-- </a-tooltip> -->
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel" >{{ props.gridComponentData.temp_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="１４℃~２４℃" arrow-point-at-center>

        <span class="dataType">温度</span>
        </a-tooltip>
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel" >{{ props.gridComponentData.tvoc_value|| "-" }}</span>
        <a-tooltip placement="topLeft" title="０．０６mg/m３" arrow-point-at-center>

        <span class="dataType">	TVOC</span>
        </a-tooltip>
      </div>
    </a-col>
    <a-col class="gutter-row"  :span="6">
      <div class="gutter-box" >
        <span class="dataLabel">{{ props.gridComponentData.water_state}}</span>
        <!-- <a-tooltip placement="topLeft" title="Prompt Text" arrow-point-at-center> -->

        <span class="dataType">水浸变送器</span>
        <!-- </a-tooltip> -->
      </div>
    </a-col>
    </a-row>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  selShow: {
    type: Boolean,
    default: true,
  },
  gridComponentData:{
    type:Object,
    default:()=>{}
  }
});
</script>
<style lang="scss" scoped>
.gutter-box{
    width: 100%;
    height: 70px;
    border-radius: 10px;
    background: rgba(7, 189, 250, 0.1);
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .dataLabel{
        font-size: 16px;
        margin-bottom: 10px;
        font-weight: 400;
        color: rgba(0, 186, 119, 1);
    }
    .dataType{
        font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: rgba(150, 169, 186, 1);
cursor: pointer;
    }
}
</style>