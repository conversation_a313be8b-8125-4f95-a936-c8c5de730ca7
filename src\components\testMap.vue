<script setup lang="ts" name="Map">
import type { VNode } from "vue";
import { ref, onMounted } from "vue";

import { createVNode, render, watch } from "vue";
import * as echarts from "echarts";
import geoJson from "../assets/shanghai.json"; // 为刚刚下载的json文件，需正确引入路径
import "echarts-gl";
import echartsGL from "echarts-gl";
import mapBgImg from "../assets/map.png";
import dw from "../assets/dw.png";
import dw1 from "../assets/dw1.png";
import dw2 from "../assets/dw2.png";
const mapChart = ref<HTMLElement>();
const props = defineProps({
  height: {
    type: String,
    default: "573",
  },
});
watch(
  () => props.height,
  (newHeight) => {
    // alert(2)
    mapChart.value.style.height = `${newHeight}px`; // 更新容器高度
    // alert(2)
    if (myChart) {
      myChart.resize(); // 更新图表大小以匹配容器大小变化
    }
  }
);
const emit = defineEmits(["city", "changeStatus"]);
const showData = ref(true);
// const emit = defineEmits(["changeStatus"]);
let myChart = null;
// const mapCharts = ref(null);
const initMap = async (status?: Boolean) => {
  myChart = echarts.init(mapChart.value);

  // 绘制图表
  let option = {
    geo3D: {
      show: true,
      map: "centerMap",
      seriesIndex: 1,
      zoom: 6,
      // regionHeight: 2,
      // show: true,
      regionHeight: 3, // 地图高度
      backgroundColor: "#493406",
      zlevel: -1,
      label: {
        show: true,
        distance: 200,
        distanca: 0,
        fontSize: 12,
        color: "#ffffff",
      },
      itemStyle: {
        // color: '#176efa', // 地图背景颜色
        borderWidth: 1, // 分界线wdith
        borderColor: "#ffffff", // 分界线颜色
        // opacity: 1,
        areaColor: "#272235",
        shadowColor: "#000000", // 阴影颜色
        shadowBlur: 10, // 阴影模糊度
        shadowOffsetX: 5, // 阴影X轴偏移
        shadowOffsetY: 5,
      },

      // emphasis: {
      //   label: {
      //     show: true, // 是否显示高亮
      //     textStyle: {
      //       color: "#fff", // 高亮文字颜色
      //     },
      //   },
      //   itemStyle: {
      //     color: "#4091BA", // 地图高亮颜色
      //     borderWidth: 10, // 分界线wdith
      //     borderColor: "white", // 分界线颜色
      //   },
      // },
      //     itemStyle: {
      //       // 三维地理坐标系组件 中三维图形的视觉属性，包括颜色，透明度，描边等。
      //       //   color: "rgba(37, 37, 38,1)", // 地图板块的颜色
      //       //   opacity: 0.5, // 图形的不透明度 [ default: 1 ]
      //       borderWidth: 0.5, // (地图板块间的分隔线)图形描边的宽度。加上描边后可以更清晰的区分每个区域   [ default: 0 ]
      //       borderColor: "rgba(219, 246, 255, 0.2)", // 图形描边的颜色。[ default: #333 ]
      //       areaColor: '#99CCFF',
      // // borderColor: '#444'
      //     },
      //     emphasis: {
      //       label: {
      //         show: true,
      //         color: "#FFFFFF", //悬浮字体颜色
      //         fontSize: 12,
      //       },
      //       itemStyle: {
      //         // color: "rgba(0, 111, 168, 0.5)",
      //         // opacity: 1,
      //       },
      //     },
      // shading: "lambert",
      light: {
        main: {
          color: "#fff",
          intensity: 2,
          shadow: true,
          shadowQuality: "ultra",
          alpha: 40,
          beta: 10,
        },
        ambient: {
          color: "skyblue",
          intensity: 0.2,
        },
      },
      // zlevel: 70,

      regions: [],
      viewControl: {
        distance: 80, // 相机距离物体的距离
        alpha: 50, // 三维场景水平方向的旋转角度
        beta: 10, // 三维场景垂直方向的旋转角度
        center: [-3, 5, 22],
        animation: true, // 是否开启动画。[ default: true ]
        animationDurationUpdate: 1000, // 过渡动画的时长。[ default: 1000 ]
        animationEasingUpdate: "cubicInOut",
      },

      shading: "realistic",
      // 真实感材质相关配置 shading: 'realistic'时有效
      realisticMaterial: {
        detailTexture: mapBgImg, // 纹理贴图
        textureTiling: 1, // 纹理平铺，1是拉伸，数字表示纹理平铺次数
        roughness: 0, // 材质粗糙度，0完全光滑，1完全粗糙
        metalness: 0, // 0材质是非金属 ，1金属
        roughnessAdjust: 0,
      },
      // lambertMaterial: {
      //   detailTexture: mapBgImg,
      // },
    },

    series: [
      {
        type: "scatter3D",
        coordinateSystem: "geo3D",
        // symbolSize: 20,
        animation: true,
        animationDurationUpdate: 500,
        geo3DIndex: 0,
        silent: false,
        // symbolSize: 10,
        // zlevel: 80,
        // symbol: 'image://https://your-image-url.com/your_image.png',
        symbolSize: [90, 0],
        itemStyle: {
          depthTest: false, // 关闭深度测试（防止被地图遮挡）
          color: "transparent",
        },
        label: {
          // 点击标记点显示队伍名称
          show: status,
          position: "top",
          distance: -60,
          formatter(params) {
            return "2";
          },
          textStyle: {
            color: "transparent",
            padding: [15, 18],
            backgroundColor: {
              image: dw,
            },
          },
        },
        data: [[121.43752, 31.179973]],
      },
      {
        type: "scatter3D",
        coordinateSystem: "geo3D",
        // symbolSize: 10,
        // zlevel: 80,
        // symbol: 'image://https://your-image-url.com/your_image.png',
        symbolSize: [150, 80],
        itemStyle: {
          depthTest: false, // 关闭深度测试（防止被地图遮挡）
          color: "transparent",
        },
        label: {
          // 点击标记点显示队伍名称
          show: status,
          position: "top",
          distance: -60,
          formatter(params) {
            return "市局";
          },
          textStyle: {
            color: "rgba(255, 255, 255, 1)",
            fontSize: 14,
            padding: [15, 80],
            backgroundColor: {
              image: dw1,
            },
          },
        },
        data: [[121.43752, 31.179973]],
      },
      {
        type: "map3D", // 加载series数据
        // seriesIndex: 1, // 第一个系列
        // selectedMode: "single",
        viewControl: {
          distance: 80, // 相机距离物体的距离
          alpha: 50, // 三维场景水平方向的旋转角度
          beta: 10, // 三维场景垂直方向的旋转角度
          // center: [5, 6, 22],
        center: [-3, 5, 22],

        },
        map: "centerMap",
        regionHeight: 0,
        // boxHeight: 500,
        itemStyle: {
          // 三维地理坐标系组件 中三维图形的视觉属性，包括颜色，透明度，描边等。
          color: "rgba(37, 37, 38,1)", // 地图板块的颜色
          opacity: 0, // 图形的不透明度 [ default: 1 ]
          // borderWidth: 3, // (地图板块间的分隔线)图形描边的宽度。加上描边后可以更清晰的区分每个区域   [ default: 0 ]
          //   borderColor: "#ffffff", // 图形描边的颜色。[ default: #333 ]
          shadowColor: "rgba(100,255,238,0.5)",
          shadowBlur: 5,
        },
        data: [
          {
            name: "奉贤区",
            value: [121.458472, 30.912345, 80],
            num: null,
          },
        ],
        label: {
          show: true,
          color: "#FFFFFF",
          formatter: "{b}",
          depthValue: 1,
        },
        regions: [],
        zlevel: 0,
      },
      // {
      //   type: "scatter3D",

      //   coordinateSystem: "geo3D",
      //   data: [],
      //   // symbol: "pin",
      //   symbolSize: 20,
      //   animation: true,
      //   animationDurationUpdate: 500,
      //   geo3DIndex: 0,
      //   silent: false,
      //   // itemStyle: {
      //   //   opacity: 1,
      //   //   width: 1,
      //   // },
      //   shading: "lambert",
      //   label: {
      //     show: true,
      //     position: "right",
      //     distance: 200, // 距离图形元素的距离
      //     color: "#ff0000",
      //     backgroundColor: "rgba(255, 255, 255, 0)",
      //     rich: {
      //       get: {
      //         color: "#00E3FF",
      //         lineHeight: 12,
      //       },
      //       set: {
      //         color: "#FFF100",
      //         lineHeight: 12,
      //       },
      //       date11: {
      //         color: "#FFFFFF",
      //         lineHeight: 12,
      //       },
      //     },
      //     zlevel: 6,
      //   },
      //   emphasis: {
      //     itemStyle: {
      //       // color: "#FFF200",
      //     },
      //     label: {
      //       show: false,
      //     },
      //   },
      //   zlevel: 6,
      // },
    ],
  };
  // myChart.showLoading();

  echarts.registerMap("centerMap", geoJson);
  // myChart.hideLoading();

  var mapFeatures = echarts.getMap("centerMap").geoJson.features;
  var geoCoordMap = {};
  mapFeatures.forEach(function (v) {
    // v.properties.center = _this.convertCoord(v.properties.center)
    // 地区名称
    var name = v.properties.name;
    // 地区经纬度
    geoCoordMap[name] = v.properties.center;
  });
  //   let currentIdx = -1;
  //   function highlightNext() {
  //     if (currentIdx++ >= 0) {
  //       // 防止下标增加到超过区域数组长度
  //       currentIdx %= mapFeatures.length;
  //       console.log(mapFeatures[currentIdx]);
  //     }
  //     highlightRegion(currentIdx); // 此处的currentIdx经过if判断，已经+1了
  //     setTimeout(highlightNext, 5000);
  //   }

  //   function highlightRegion(index) {
  //     // 高亮地图区域，改变对应的散点样式
  //     let regionName = mapFeatures[index].properties.name; // 高亮的区名

  //     option.geo3D.regions = [
  //       // 高亮3D地图对应的区块
  //       {
  //         name: regionName,
  //         label: {
  //           show: false,
  //           color: "red",
  //         },
  //         itemStyle: {
  //           color: "#ffffff",
  //           opacity: 0,
  //         },
  //       },
  //     ];

  //     // 当前高亮区域有散点，需删除stroke动效中的该条数据并在fill动效中增加该条数据
  //     option.series[0].data = scatterData.filter(
  //       (item) => item.name !== regionName
  //     ); // stroke散点数据
  //     option.series[1].data = scatterData.filter(
  //       (item) => item.name === regionName
  //     ); // fill散点数据

  //     myChart.setOption(option);
  //     myChart.dispatchAction({
  //       // 触发散点高亮事件，改变标签样式
  //       type: "showTip",
  //       seriesIndex: 0, // series中的第二个图层（fill散点图层）
  //       dataIndex: 0, // 高亮第一条数据，由于fill散点图层只有一条数据，这里可以不指定
  //     });
  //     myChart.dispatchAction({
  //       type: "highlight",
  //       seriesIndex: 0,
  //       dataIndex: 0,
  //     });
  //     // 显示弹框
  //     myChart.dispatchAction({
  //       type: "showTip",
  //       seriesIndex: 0,
  //       dataIndex: 0,
  //     });
  //   }
  //   myChart.getZr().on("click", (params) => {
  const index = ref();

  myChart.on("click", function (params) {
    click_type = true
    emit("changeStatus", false);
    option.series[0].label.show = false;
    option.series[1].label.show = false;
    index.value = params.dataIndex;
    let newObj = {
      name: params.name,
      itemStyle: {
        opacity: 1, // 透明度
        // borderWidth: 10, //分界线宽度
        borderType: [5, 5],
        shadowColor: "rgba(0, 0, 0, 0.5)",
        shadowBlur: 10,
        z: 0,
        areaColor: "#99CCFF",
        // borderColor: '#444',
        // color: "rgba(232, 212, 60, 0.6)",
        // color:'#ffffff'
      },
      label: {
        color: "yellow", //文字颜色
        fontSize: 16,
      },
      height: 4,
      // }
    };
    emit("city", { data: { name: params.name } });
    if (
      option.geo3D.regions &&
      option.geo3D.regions[0] &&
      option.geo3D.regions[0].name &&
      option.geo3D.regions[0].itemStyle &&
      option.geo3D.regions[0].name == params.name
    ) {
      option.geo3D.regions[0] = { name: params.name };
    } else {
      option.geo3D.regions[0] = newObj;
    }
    myChart.setOption(option, false);
    //     myChart.setOption({
    //       series: {
    //          data: [
    //             {
    //                name: params.name,
    //                itemStyle: {
    //                   color: '#539efe9a'
    //                },
    //                label:{
    //                   show:false
    //                }
    //             }
    //          ],
    //       }
    //    })
  });
  let click_type;
  myChart.getZr().on("click", (params) => {
    click_type = false;
    setTimeout(check, 500);
    function check() {
      if (click_type != false) return;
      if (!params.target) {
        if (click_type != false) return;
        option.series[0].label.show = true;
        option.series[1].label.show = true;
        emit("city", { data: { name: "上海市" } });
        option.geo3D.regions = [];
        myChart.setOption(option, false);
        // initMap()
        emit("changeStatus", true);
        return;
      }
    }
  });
  myChart.getZr().on("dblclick", (params) => {
    click_type = true;
    if (!params.target) {
      option.series[0].label.show = false;
      option.series[1].label.show = false;
      emit("city", { data: { name: "上海市" } });
      option.geo3D.regions = [];
      myChart.setOption(option, false);
      emit("changeStatus", false);
      return;
      // initMap()
    }
  });
  //   myChart.off('dblclick').on('dblclick',  (event)=> {
  //     //  mapClick_type = true;   //自己自定义的，不用管
  //     option.series[0].label.show = false;
  //       option.series[1].label.show = false;
  //       emit("city", { data: { name: "上海市" } });
  //       option.geo3D.regions = [];
  //       myChart.setOption(option, true);
  //      event.event.stop();   //防止事件下钻
  //      return;
  //  });
  // myChart.getZr().on("dbclick", (params) => {
  //   console.log(params);
  //   if (!params.target) {
  //     emit("city", { data: { name: "上海市" } });
  //     // showData.value = true;
  //     option.series[0].label.show = true;
  //     option.series[1].label.show = true;
  //     // option.series.remove(-2,-1);
  //     option.geo3D.regions = [];
  //     // myChart.setOption(option, true);
  //     // initMap()
  //   }
  // });
  myChart.setOption(option, true);
  // window.addEventListener('resize',()=>{
  //   myChart.resize()

  // },false)
  //   highlightNext();
};
// const emit = defineEmits(["changeStatus"]);
const mapChartSize = () => {
  if (myChart) {
    myChart.resize(); // 图表自适应大小
  }
};
defineExpose({
  initMap,
});
onMounted(() => {
  // 初始化地图
  initMap();
  mapChartSize();
});
</script>
 
<template>
  <div ref="mapChart" id="mapChart" class="mapChart" style="width: 100%;height: 700px;" />
</template>
 
<style lang="scss" scoped>
::v-deep(.map-tooltip) {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
}
</style>