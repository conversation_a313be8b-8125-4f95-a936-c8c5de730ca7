<template>
  <div>
    <div class="lineineChart" ref="lineChart">
    </div>
    <modalss ref="modalComponents" :titles="props.title" :type='5'>
    </modalss>
  </div>

</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import * as echarts from "echarts";
import modalss from "../components/modal.vue";
import * as api from "../api/index.ts";
import dayjs from "dayjs";

const props = defineProps({
  selShow: {
    type: Boolean,
    default: true,
  },
  title:{
    type:String,
    default:''
  },
  status:{
    type: Boolean,
    default: true,
  },
});
const modalComponents = ref(null);
const lineChart = ref(null);
const initLineChart = () => {
  const chart = echarts.init(lineChart.value);
  const option = {
    color: "#42DFFF",
    legend: {
      x: "center",
      y: "bottom",
      //   top:20,
      textStyle: {
        color: "#96A9BA",
      },
    },
    xAxis: {
      type: "category",
      data: XData.value,
      axisPointer: {
        value: "2016-10-7",
        snap: true,
        lineStyle: {
          color: "#7581BD",
          width: 2,
        },
        label: {
          backgroundColor: "#7581BD",
        },
      },
    },
    toolbox: {
      // left:'10%',                   // 工具栏组件距离容器左侧的距离
        right:'40%',                  // 工具栏组件距离容器右侧的距离
        // top:'10%', 
      feature: {
        // saveAsImage: {},
        myTool1: {
          show: props.status,
        //   title: "详情",
        top:'10%', 
          icon: "M10.3335 1H15.0002V5.66666 M5.66666 1H1V5.66666 M10.3335 15H15.0002V10.3333 M5.66666 15H1V10.3333 M14.9999 1L9.94434 6.05555 M6.05555 9.94446L1 15",
          onclick: function () {
            // getChartExcel();
            // alert(22)
            modalComponents.value.init()
          },
        //   iconStyle:{
        //     color:'#FFFFF'
        //   }
        },
      },
    //   right: "10%",
    // top:-6
    // iconStyle:{
    //         color:'white',                // 图形颜色
    //         borderWidth:1,              // 图形描边宽度
    //         borderColor:'white',          // 图形描边颜色
    //         borderType:'solid',                // 图形透明度       
    //     },
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,
        lineStyle: { type: "dashed", color: "#96A9BA" },
        color: "#96A9BA",
      },
    },
    grid: {
      left: "10%",
      right: "1%",
      bottom: "10%",
      top: "15%",
    },
    series: [
      {
        data: lineData.value,
        type: "line",
        smooth: true,
        areaStyle: {
          // 使用方法二的写法
          color: {
            type: "linear",
            x: 0, //右
            y: 0, //下
            x2: 0, //左
            y2: 1, //上
            colorStops: [
              {
                offset: 0,
                color: "rgba(0, 227, 255, 0.3)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0, 227, 255, 0)", // 100% 处的颜色
              },
            ],
          },
        },
      },
    ],
  };
  option && chart.setOption(option, true);
};
const XData = ref([]);
const lineData = ref([]);
const init = async (record?: any,status?:Boolean) => {
  setTimeout(async () => {
    // alert(record.typeId) 
    const today = dayjs();
    const data = {
      typeId: record.typeId || "",
      areaId: record.areaId || "",
      dateType: record.dateType,
    };
    if (record.dateType == 1) {
      data.startTime =
        record.startTime || today.subtract(status?9:4, "year").format("YYYY");
      data.endTime = record.endTime || today.format("YYYY");
    } else if (record.dateType == 2) {
      data.startTime =
        record.startTime || today.subtract(status?9:4, "year").format("YYYY-MM");
      data.endTime = record.endTime || today.format("YYYY-MM");
    } else {
      data.startTime =
        record.startTime || today.subtract(status?9:4, "year").format("YYYY-MM-DD");
      data.endTime = record.endTime || today.format("YYYY-MM-DD");
    }
    const res = await api.archives_use_situation(data);

    XData.value = [];
    lineData.value = [];
    res?.data.forEach((item) => {
      XData.value.push(item.dateTime);
      lineData.value.push(item.totalNum);
    });

    initLineChart();
  }, 100);
};

defineExpose({
  init,
});
onMounted(() => {
  init();
  initLineChart();
});
</script>
<style scoped>
.lineineChart {
  width: 100%;
  height: 100%;
}
</style>