import axios from "axios";
// import { ElMessage } from "element-plus";
import { message } from "ant-design-vue";
// import router from "@/router";
// import router from './router'


const request = axios.create({
  // 此处的 '/api' 和 vite.config.js 的配置相关联
  baseURL: "http://**************:8866/",//测试
  // baseURL: "http://*************:8866/large-api/",//打包
  // baseUrl:process.env.VUE_APP_BASE_API,
  // baseUrl:window.location.hostname,
  // baseURL: "http://*************/",
  timeout: 50000,
});

// 数据请求拦截
request.interceptors.request.use((config) => {
  // console.log();

  if (localStorage.getItem("supplierToken")) {
    config.headers["supplier_token"] = `${localStorage.getItem(
      "supplierToken"
    )}`;
  }
  // 1. 返回config对象
  // 2. 可以设置携带 token 信息
  return config;
});

// 返回响应数据拦截
request.interceptors.response.use(
  (response) => {
    // console.log("request.js打印返回信息" , response);
    // 简化返回数据
    // if (response.data.code == "0" || response.data.code == "1") {
    if (response.data.code == "200") {
      return Promise.resolve(response.data);
    } else if (response.data.code == "401000") {
      if (response.data.code == "401000") {
        // router.push({
        //   path: "/login",
        // });
      }
      // message.error(response.data.message);
      return Promise.reject();
      // return Promise.reject(new Error(response.data.message || "出错了"));
    } else if(response.data.code == "1"){
      // message.error(response.data.message);
      return  Promise.resolve(response.data);
      // return Promise.reject(new Error(response.data.message || "出错了")) ;
    }else{
       message.error(response.data.message);
       return  Promise.resolve(response.data);
    }
  },
  // 错误执行
  (error) => {
    if (error.response.data.code) {
      switch (error.response.data.code) {
        case 401000:
          message.error("用户信息");
          // ElMessage({
          //   type: 'error',
          //   message: '请求路径找不到！',
          //   showClose: true
          // });
          break;

        case 500:
          message.error("服务器内部报错！");
          // ElMessage({
          //   type: 'error',
          //   message: '服务器内部报错！',
          //   showClose: true
          // });
          break;

        // 还可以自己添加其他状态码
        default:
          break;
      }
    }
    return Promise.reject(new Error(error.message));
  }
);

// 暴露对象
export default request;
