<template>
  <div class='tooltip-bg' ref="tipHtmls">
    <div class='tools-type'>{{ data.name }}</div>
    <div style="padding: 0px 10px 0 10px;overflow: auto;">
      <div @click="handleClick(index,item)" v-for="(item,index) in data.data.data" :key="index" :class="selType==index?'selType':'noSel'">
      <span>{{item.typeName || '-' }}</span>
      <span>{{ item.number || 0 }}</span>
    </div>
    </div>

  </div>
</template>
<script setup lang="ts">
import { computed } from "vue";
import { ref, onMounted, createVNode, render } from "vue";
import { eventBus } from "@/utils/eventBus.js"
import bus from 'vue3-eventbus'
const prop = defineProps({
  data: {
    type: Object,
    defaul: () => {},
  },
});

const emit = defineEmits();

const selType = ref();
const handleClick = (index?: Number,record?:any) => {
  selType.value = index;
  bus.emit('refreshPage', record)
};
</script>
<style lang="scss" scoped>
.tooltip-bg {
  background: url("../assets/tool.png") no-repeat;
  background-size: 100% 100%;
  min-width: 254px;
  // height: 182px;
  // overflow: scroll;
  padding: 0;
  padding-top: 10px;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 10px;
  // background: transparent;
  .tools-type {
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    box-sizing: border-box;
    color: rgba(255, 255, 255, 1);
    padding-left: 10px;
margin-bottom: 10px;
    
      // max-height: 28px;
    background: linear-gradient(90deg, #1E6F98 0.2%, rgba(30, 111, 152, 0.0001) 100%);

    line-height: 28px;
  }
  .selType,
  .noSel {
    // ba
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 10px;
    align-items: center;
    height: 28px;
    font-weight: 400;
   
    font-size: 14px;
  }
  .noSel {
    color: rgba(150, 169, 186, 1);
  }
  .selType {
    background: rgba(37, 143, 178, 0.2);
    color: rgba(255, 255, 255, 1);
  }
}
</style>