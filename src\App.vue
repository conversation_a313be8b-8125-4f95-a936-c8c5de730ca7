
<template>
  <!-- <ScaleBox width="1920" height="1080" autoScale > -->
  <a-config-provider style="height: 100%;" :locale="zhCN">

  <ScaleBox width="1920" height="1080" autoScale :fit="'cover'" :delay="1" :isFlat="true">
    <RouterView />
  </ScaleBox>
</a-config-provider>
<modals ref="fault_one" :faultOneData="faultOneData" :type="4" titles="报警信息" />
</template>

<script setup lang="ts">
import ScaleBox from 'vue3-scale-box';
import { RouterView } from 'vue-router';
import { onMounted, onUnmounted,ref } from 'vue';
import zhCN from "ant-design-vue/es/locale/zh_CN";
import modals from "./components/modal.vue";
import * as api from "./api/index.ts";

// 设计稿宽度
const DESIGN_WIDTH = 1920;
// 基准fontSize
const BASE_FONT_SIZE = 100;

// 设置rem函数
const setRem = () => {
  const clientWidth = window.innerWidth;
  // 计算缩放比例
  const scale = clientWidth / DESIGN_WIDTH;
  // 设置根元素fontSize，限制最大最小值
  document.documentElement.style.fontSize = `${Math.min(Math.max(scale * BASE_FONT_SIZE, 50), 150)}px`;
};
const fault_one=ref(null)
const faultOneData=ref(null)
const faultOne=async()=>{
  let res=await api.fault_one()
  // fault_one.value
  if(res.data){
  fault_one.value.init()
    faultOneData.value=res.data || {}
  }
}
onMounted(() => {
  setRem();
  // fault_one.value.init()
  setInterval(()=>{
    faultOne()
  },10000)
  window.addEventListener('resize', setRem);
});

onUnmounted(() => {
  window.removeEventListener('resize', setRem);
});
</script>

<style scoped>
::v-deep .scale-box-container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  transform-origin: 0 0 !important;
}

* {
  box-sizing: border-box !important;
}
</style>

<style>
::-webkit-scrollbar {
    width: 2px;
    height: 8px;
}
/* 两个滚动条交接处 -- x轴和y轴 */
::-webkit-scrollbar-corner {
    background-color: transparent;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 2px #05152f;
    background: rgba(24, 80, 112, 1);
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 2px #05152f;
    border-radius: 10px;
    background: rgba(24, 80, 112, 1);
}
.ant-input::placeholder {
  color: white; /* 颜色 */
}
input::placeholder {
  color: white; /* 颜色 */
}
/* 全局样式 */
body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}
</style>

<style >
#app{
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  max-width: 100% !important;
}
body {
    margin: 0;
    padding: 0;
}
button:focus {
  outline: none;
}
</style>

