<template>
  <div>
    <a-modal v-model:visible="state.visible" :footer="null" :width="showType?'40%':'70%'" :closable="false">
      <img class="closeImg" src="../assets/close.png" @click="handleClose" alt="">

      <div v-if="showType">
        <div class="modalHead">
          <div class="modalText">{{ props.titles ||'-' }}</div>
        </div>
        <barLineChart :status='false' ref="barLineCharts" style="height: 472px;" v-if="props.type==1"></barLineChart>
        <lineChart :status='false' ref="lineCharts" style="height: 472px;" v-else-if="props.type==5"></lineChart>

        <a-form layout="inline" style="height: 400px;display: flex;align-items: center;justify-content: center;" v-else-if="type==2">
          <a-form-item v-if="props.dateType=='year'" name="yearRange" label="年份范围">
            <a-range-picker class="picker-input" @change="onRangeChange" v-model:value="dateTime" picker="year" value-format="YYYY" />
          </a-form-item>
          <a-form-item v-else-if="props.dateType=='month'" name="yearRange" label="年月份范围">
            <a-range-picker class="picker-input" @change="onRangeChange" v-model:value="dateTime" picker="month" value-format="YYYY-MM" :locale="locale"/>
          </a-form-item>
          <a-form-item v-else name="yearRange" label="年月日份范围">
            <a-range-picker class="picker-input" @change="onRangeChange" v-model:value="dateTime" value-format="YYYY-MM-DD" />
          </a-form-item>
        </a-form>
        <!-- <div class="batch_selection" v-else-if="type==3">
          <div v-for="(item,index) in batchSelection" :key="index">
           <img style="width: 100px;height: 100px;" src="../assets//video.png" alt="">
          </div>
        </div> -->
        <div v-else-if="type==3">
          <h3 style="color: white;">摄像头列表</h3>
         <a-flex wrap="wrap" gap="small" >
          <div v-for="(item,index) in batchSelection" :key="index" style="display: flex;flex-direction: column;align-items: center;" @click="handleAdd(item)">
           <img style="width: 100px;height: 100px;" src="../assets//SelVideo.png" alt="">
           <div style="color: white;width: 160px;"> {{ item.cameraName }}</div>
          </div>
         
        </a-flex>
        <h3 style="color: white;margin-top: 30px;">轮播摄像头</h3>
         <a-flex wrap="wrap" gap="small" >
          <div v-for="(item,index) in selBatchSelection" :key="index" style="display: flex;flex-direction: column;align-items: center;" @click="handleDelete(index)">

           <img style="width: 100px;height: 100px;" src="../assets//SelVideo.png" alt="">
           <div style="color: white;width: 160px;"> {{ item.cameraName }}</div>
          </div>
         
        </a-flex>
        <a-button @click="handleSubmit" style="margin: 0 auto;display: flex;margin-top: 10px;" type="primary">确认</a-button>
      </div>
        <div class="faultOneData" v-else>

          <a-descriptions>
            <a-descriptions-item label="设备名称">{{ faultOneData.deviceName ||'-' }}</a-descriptions-item>
            <a-descriptions-item label="故障内容">{{ faultOneData.faultContent ||'-' }}</a-descriptions-item>
            <a-descriptions-item label="故障时间">{{faultOneData.faultDate ||'-'	 }}</a-descriptions-item>
            <a-descriptions-item label="故障标题">{{faultOneData.faultTitle ||'-'}}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
      <div v-else>
        <div class="modalHead">
          <div class="head-sel-arr">
            <div v-for="(item,index) in arr" :key="index" @click="handleClick(index)" :class="selNum==index?'selNum':'noSelnum'">{{ item }}</div>
          </div>
        </div>

        <div v-if="selNum==0">

          <a-input-search style="width: 400px;margin: 20px 0;" v-model:value="state.searchAll" placeholder="请输入内容" enter-button="查询" size="large" @search="archivesList(state.paginationOpt.defaultCurrent,state.paginationOpt.defaultPageSize)" />

          <a-table :columns="state.columns" :data-source="state.dataSource" :pagination='state.paginationOpt' class="modalTable" :rowClassName="() => { return 'rowClass-table' }" size="middle" :scroll="{  y: 540 }">
            <template #bodyCell="{ column, record,index }">
              <template v-if="column.key=='index'">
                {{ index+1 }}

              </template>
              <template v-if="column.key=='action'">
                <a-button type="link" size="small" @click="handleKj(record)">开架</a-button>

              </template>
            </template>
          </a-table>
        </div>
        <iframe style="width: 100%;height: 700px;" v-else :src="'http://'+baseURL+':8908'+'/pages/V3/device/mjj_pub.html?roomCode=6'" frameborder="0"></iframe>
      </div>

    </a-modal>
    <a-modal v-model:visible="state.visible1" :footer="null" width="40%" :closable="false">
      <img class="closeImg" src="../assets/close.png" @click="handleClose1" alt="">

      <iframe style="width: 100%;height: 700px;"  :src="flvUrl" frameborder="0"></iframe>


    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import barLineChart from "./barLineChart.vue";
import * as api from "../api/index.ts";
import request from "../utils/request";
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN'
import lineChart from "./line.vue";

import 'dayjs/locale/zh-cn'
const arr = ref(["档案检索", "密集架控制"]);
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  titles: {
    type: String,
    default: "",
  },
  dateType: {
    type: String,
    default: "",
  },
  showType: {
    type: Boolean,
    default: true,
  },
  faultOneData: {
    type: Object,
    default: null,
  },
});
const dateTime=ref([
 
dayjs().format(),dayjs().format(),

]);
const state = reactive({
  searchAll: null,
  visible1: false,
  paginationOpt: {
    showTotal: (total) => `共 ${total} 条`, // 显示总数
    defaultCurrent: 1, // 默认当前页数
    defaultPageSize: 5, // 默认当前页显示数据的大小
    total: 0, // 总数，必须先有

    showSizeChanger: true,
    showQuickJumper: true,
    // show-quick-jumper
    pageSizeOptions: ["5", "10", "15", "20"],

    onShowSizeChange: (current, pageSize) => {
      archivesList(current, pageSize);

      // this.paginationOpt.defaultCurrent = 1;
      // this.paginationOpt.defaultPageSize = pageSize;
      // this.searchCameraFrom(); //显示列表的接口名称
    },
    // 改变每页数量时更新显示
    onChange: (current, size) => {
      archivesList(current, size);

      // this.paginationOpt.defaultCurrent = current;
      // this.paginationOpt.defaultPageSize = size;
      // this.searchCameraFrom();
    },
  },
  visible: false,
  dateTime: [
  dayjs('2015/01/01'),
  dayjs('2015/01/01'),
],
  dataSource: [],
  columns: [
    {
      title: "序号",
      dataIndex: "index",
      key:'index',
      align: "left",
    },
    {
      title: "档案类型",
      dataIndex: "typeName",
      align: "left",
    },
    {
      title: "档号",
      dataIndex: "archivesNo",
      align: "left",
    },
    {
      title: "题名",
      dataIndex: "archivesName",
      align: "left",
    },
    {
      title: "年度",
      dataIndex: "archivesYear",
      align: "left",
    },
    {
      title: "位置",
      dataIndex: "archivesAddr",
      align: "left",
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      align: "left",
    },
  ],
});
const emit = defineEmits(["dateTime", "changeDate",'handleSure']);
const clearDate = () => {
  dateTime.value = [
 
 dayjs().format(),dayjs().format(),
 
 ];
};
const flvUrl = ref();
const handleKj = async (record?: any) => {
  const res = await api.archives_open_shelf({
    id: record.id,
  });
  setTimeout(async () => {
    const res1 = await api.get_camera_url({
      compactTier: record.compactTier,
    });
    state.visible1 = true;
    flvUrl.value = `http://${baseURL.value}:8908/open/player?videoUrl=${res1.data}`;
  }, 2000);
};
const batchSelection = ref(null);
const selBatchSelection=ref([])

const batchInit = async () => {
  // batchSelection.value=[]
  const res = await api.device_camera_list();
  batchSelection.value = res.data || [];
  selBatchSelection.value=[]
  batchSelection.value.forEach(item=>{
    if(item.isShow==1){
      // if(selBatchSelection.value.indexOf(item)==-1){
    selBatchSelection.value.push(item)
  // }

    }
  })
};

const handleClose = () => {
  state.visible = false;
};
const handleClose1 = () => {
  state.visible1 = false;
};
const barLineCharts = ref(null);
const lineCharts = ref(null);
const init = () => {
  state.visible = true;
  if (props.type == 1) {
   
    setTimeout(()=>{
      barLineCharts.value.init(JSON.parse(localStorage.getItem('barlin')),true);
    },100)
  }else if(props.type==5){
    setTimeout(()=>{
      lineCharts.value.init(JSON.parse(localStorage.getItem('lin')),true);
    },100)
  }
};
const selNum = ref(0);
const handleClick = (index?: Number) => {
  selNum.value = index;
  // emit('changeSelNum',selNum.value+1)
  if (index == 0) {
    archivesList();
  }
};
const onRangeChange = () => {
  emit("changeDate", dateTime.value);
};

const archivesList = async (pageNum?: any, pageSize?: any) => {
  const res = await api.archives_list({
    pageNum: pageNum,
    pageSize: pageSize,
    searchAll: state.searchAll,
  });
  // console.log(res);
  state.dataSource = res.data?.list || [];
  state.paginationOpt.total = res.data?.totalNum || 0;
};
//选择摄像头

const handleAdd=(record?:any)=>{
  // selBatchSelection.value.push(record)
  // selBatchSelection.value.indexOf(record)
  if(selBatchSelection.value.indexOf(record)==-1){
    selBatchSelection.value.push(record)
  }


}
const handleSubmit=async()=>{
  const ids=selBatchSelection.value.map(item=>item.id)

  const res=await api.device_camera_batch_show({'ids':ids})
  if(res.code==200) {
    state.visible=false
    emit('handleSure')
  }
}
const handleDelete=(index?:Number)=>{
  selBatchSelection.value.splice(index, 1);
}
defineExpose({
  init,
  clearDate,
  archivesList,
  batchInit,
});
const baseURL=ref(null)
onMounted(() => {
  // baseURL.value= sessionStorage.getItem('baseUrl')
  if(sessionStorage.getItem('baseUrl')){

baseURL.value= sessionStorage.getItem('baseUrl')
}else{
baseURL.value='**************'
}
  if(props.type==2){
    // const dateFormat = 'YYYY';
    
    if(props.dateType=='year'){
      console.log([null, dayjs(dayjs().subtract(2, "day").toDate()) || "-"]);
      
    // alert(1)
    // state.dateTime=['2025','2025']
  }
  setTimeout(()=>{
    state.dateTime = ['2022','2022'];
  },100)
  }
  emit("changeDate",[]);
  // archivesList()
});
</script>
<style>
.modalTable .ant-table {
  border: none;
  background: transparent;
}

.modalTable .ant-table table {
  background: transparent;
}
.ant-dropdown .ant-dropdown-menu {
  background: #0c3850 !important;
  padding: 0;
}
.ant-dropdown-menu-title-content {
  color: rgba(150, 169, 186, 1);
}
.ant-form-item .ant-form-item-label > label {
  color: #ffffff !important;
}
.ant-picker {
  background: rgba(32, 98, 131, 0.3);
  border-color: rgba(99, 216, 255, 0.2);
}
.ant-picker .ant-picker-input > input {
  color: #ffffff !important;
}
.ant-table-thead {
  background: transparent;
}
</style>
<style scoped lang='scss'>
:deep(.ant-table-wrapper .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before){
  width: 0;
}
:deep(.ant-table-wrapper .ant-table-tbody .ant-table-cell) {
  color: rgba(187, 207, 225, 1);
}
:deep(.ant-descriptions .ant-descriptions-item-label) {
  color: #ffffff !important;
}
:deep(.ant-descriptions .ant-descriptions-item-content) {
  color: #ffffff !important;
}
:deep(
    .ant-input-search
      > .ant-input-group
      > .ant-input-group-addon:last-child
      .ant-input-search-button
  ) {
  background: rgba(0, 126, 189, 1);
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  color: red;
}
:deep(
    .ant-table-wrapper
      .ant-table:not(.ant-table-bordered)
      .ant-table-tbody
      > tr
      > td
  ) {
  border-top: 1px solid rgba(99, 216, 255, 0.2);
}
:deep(
    .ant-table-wrapper
      .ant-table:not(.ant-table-bordered)
      .ant-table-tbody
      > tr:last-child
      > td
  ) {
  border-bottom: 1px solid rgba(99, 216, 255, 0.2);
}
:deep(.ant-pagination .ant-pagination-item-active) {
  border: none;
  background: rgba(0, 126, 189, 1);
}
:deep(.ant-pagination .ant-pagination-item-active a) {
  color: rgba(255, 255, 255, 1) !important;
}
:deep(.ant-select-single.ant-select-sm .ant-select-selector) {
  background: rgba(37, 143, 178, 0.1);
  border: none;
  color: rgba(150, 169, 186, 1);
}
:deep(
    .ant-pagination.ant-pagination-mini
      .ant-pagination-options-quick-jumper
      input
  ) {
  background: rgba(37, 143, 178, 0.1);
  border: none;
  color: rgba(150, 169, 186, 1);
}
:deep(
    .ant-pagination.ant-pagination-mini .ant-pagination-options-quick-jumper
  ) {
  color: rgba(107, 119, 133, 1);
}
:deep(.ant-pagination .ant-pagination-item a) {
  color: rgba(107, 119, 133, 1);
}
:deep(
    .ant-pagination.ant-pagination-mini
      .ant-pagination-next
      .ant-pagination-item-link,

  ) {
  // color: rgba(37, 143, 178, 0.1);
  // width: 24px;
  // height: 22px;
  // border: none;
  // background: rgba(37, 143, 178, 0.1);
  color: rgba(107, 119, 133, 1);
}
:deep(
    .ant-pagination.ant-pagination-mini
      .ant-pagination-prev
      .ant-pagination-item-link
  ) {
  color: rgba(107, 119, 133, 1);
}
:deep(.ant-pagination-total-text) {
  color: rgba(7, 189, 250, 1);
}
:deep(.ant-table-tbody) {
  > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-row-hover,
  .ant-table-row-hover > td {
    color: #ffffff;
    background: none;
  }
}
:deep(.picker-input) {

.ant-picker-input input::placeholder {

  color: white;

}

}
:deep(.ant-table-wrapper .ant-table-thead >tr>th ) {
  // color: #4098f0;
  color: rgba(150, 169, 186, 1);
  font-weight: 400;
  font-style: Regular;
  font-size: 14px;

  background: rgba(37, 143, 178, 0.1) !important;
  border: none;
  box-shadow: none;
}
:deep(
    :where(.css-dev-only-do-not-override-1p3hq3p).ant-table-wrapper
      .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before
  ) {
  width: 0;
}
</style>
<style lang="scss" >
// :deep(.rowClass-table >.ant-table-thead){
//   background: transparent !important;
// }

// :deep()
// .modalTable .ant-table {
//   background: transparent;
// }
// .modalTable .ant-table table {
//   background: transparent;
// }
:deep(
    .ant-table-wrapper
      .ant-table.ant-table-bordered
      > .ant-table-container
      > .ant-table-content
      > table
  ) {
  border: none;
}

.ant-input {
  background: #072e3e;
  border-color: #072e3e;
  &::input-placeholder {
    color: red;
  }
}
.batch_selection {
  display: flex;
}
:deep(.ant-descriptions .ant-descriptions-item-label) {
  color: white !important;
}
.faultOneData {
  padding: 20px;
}
.modalHead {
  width: 100%;
  height: 60px;
  background: url("../assets//modalHead.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .modalText {
    line-height: 60px;
    text-align: center;
    font-weight: 600;
    font-style: Semibold;
    font-size: 18px;
    color: #ffffff;
  }
}
.ant-modal .ant-modal-content {
  background: none;
  padding: 9px 20px 20px 20px;
  background: url("../assets/modalBg.png") no-repeat;
  background-size: 100% 100%;
  .closeImg {
    width: 30px;
    height: 30px;
    position: absolute;
    right: -13px;
    top: -7px;
    cursor: pointer;
  }
}
.head-sel-arr {
  display: flex;
  position: relative;
  .selNum,
  .noSelnum {
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0%;
    color: #96a9ba;
    margin: 0 10px;
    cursor: pointer;
  }
  .selNum {
    color: #ffffff;
    position: relative;
    &::after {
      content: " ";
      width: 50px;
      height: 24px;
      background: url("/src/assets/sel.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 3px;
      bottom: -22px;
    }
  }
}
</style>