<template>
  <div class="pieMain" id="pieMain" ref="pieMain">
  </div>

</template>

<script setup lang="ts">
import { ref, onMounted, createVNode, render, h } from "vue";
import tooltip from "./tooltip.vue";
import * as echarts from "echarts";
import { eventBus } from "../utils/eventBus.js"
import * as api from "../api/index.ts";
import bus from 'vue3-eventbus'
const props = defineProps({
  selShow: {
    type: Boolean,
    default: true,
  },
  // areaId:{
  //   type:String,
  //   default:''
  // }
});
const typeName=ref('')
const pieMain = ref(null);
var chart = null;
const initMap = () => {
  var tipHtmls = ref(null);
  chart = echarts.getInstanceByDom(pieMain.value);
  if (chart == null) {
    chart = echarts.init(pieMain.value);
  }

  const option = {
    tooltip: {
      trigger: "item",
      point: "center",
      enterable: true, //允许鼠标进入提示悬浮层中
      showContent: true,
      confine: true,
      // position: ['50%','80%'],
      extraCssText:
        //额外附加到浮层的css样式，此处为为浮层添加阴影及padding
        "padding:0;background:transparent;border:none;",
      // triggerOn: "click",
      triggerOn: "mousemove|click",
      formatter: (params?: any) => {

        // 经纬度太长需要对位数进行截取显示，保留七位小数
        // 需要绑定点击事件
        // var tipHtml = "";
        // tipHtml = `<div class='tooltip-bg'>
        //   <div class='tool-type' >${params.name}</div>
        //             ${params.data.data
        //               .map((item1, i) => {
        //                 if (selType.value == i) {
        //                   return `
        //                     <div onClick="handleMouseover(${i})" class='selType'>
        //                     <span>1</span>
        //                     <span>2</span>
        //                     </div>
        //                     `;
        //                 } else {
        //                   return `
        //                     <div onClick="handleMouseover(${i})" class='noSel'>
        //                     <span>1</span>
        //                     <span>2</span>
        //                     </div>
        //                     `;
        //                 }
        //               })
        //               .join("")}
        //   </div>`;
        // const tipHtml = tipHtmls.Value
        // ToolTip:ToolTip.vue文件
        // return tipHtml;
        const tip = createVNode(tooltip, {
          data: params,
          // onHandleClick:handleClick
        });
        const mountNode = document.createElement("div");
        render(tip, mountNode);
        
        return mountNode;
      },
    },
    legend: {
      top: "20%",
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      icon: "circle",
      // right:'10%',
      // left: 'right'
      // left:'20%',
      right: "right",
      textStyle: {
        color: "#96A9BA",
      },
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["40%", "50%"],
        avoidLabelOverlap: false,
        roseType: "area",
        label: {
          show: false,
          position: "center",

          color: "#fff",
          fontStyle: "normal",
          fontWeight: "normal",
          fontFamily: "sans-serif",
          fontSize: 12,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: "bold",
            color: "rgba(150, 169, 186, 1)",
            formatter: "{d}%",
          },
        },
        labelLine: {
          show: false,
        },
        // data: [
        //   { value: 1048, name: "Search Engine", data: [2, 3, 4, 5] },
        //   { value: 735, name: "Direct", data: [2, 3, 4, 5] },
        //   { value: 580, name: "Email", data: [2, 3, 4, 5] },
        //   { value: 484, name: "Union Ads", data: [2, 3, 4, 5] },
        //   { value: 300, name: "Video Ads", data: [2, 3, 4, 5] },
        // ],
        data:dataList.value
      },
    ],
  };

  option && chart.setOption(option, true);
  chart.off('click')
  chart.on('click', (params) =>{
    // typeName.value=params.name
    emit("typeName", {typeName:params.name,typeId:params.data.typeId});

  })
};
const dataList=ref([])
const init=async(areaId?:String)=>{
  const res=await api.archives_type_ratio({
    areaId:areaId,
  })
  dataList.value=res.data?.map((item) => ({
      name: item.typeName,
      value: item.number,
      typeId:item.typeId,
      data:item.child
    })) || [];
    initMap()
};
const emit = defineEmits(["typeName"]);

defineExpose({
  init,
});
onMounted(() => {
  init()
  initMap();
// archives_type_ratio()
bus.on('refreshPage', (data) => {
  emit("typeName", {typeName:data.typeName,typeId:data.typeId});
});
  
});
</script>
<style scoped lang="scss">
.pieMain {
  width: 100%;
  height: 230px;
}
</style>