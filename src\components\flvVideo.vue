<template>
  <video ref="videoPlayer" width="100%" height="100%" controls></video>
</template>
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import flvjs from "flv.js";

const props = defineProps({
  url: {
    type: String,
    required: true,
  },
});

const videoPlayer = ref<HTMLVideoElement | null>(null);
let flvPlayer: flvjs.Player | null = null;

// 销毁播放器
const destroyPlayer = () => {
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }
};

// 初始化播放器 - 修复核心区域
const initPlayer = async (url: string) => {
  // 1. 恢复浏览器支持检查
  setTimeout(() => {
    if (!flvjs.isSupported()) {
      alert("当前浏览器不支持FLV播放，请使用Chrome/Firefox/Edge");
      return;
    }

    // 2. 检查视频元素是否存在
    if (!videoPlayer.value) {
      console.error("视频元素未加载");
      return;
    }

    // 3. 移除调试alert，避免阻塞执行
    // alert(url); // ❌ 导致代码中断

    destroyPlayer();

    try {
      flvPlayer = flvjs.createPlayer(
        {
          type: "flv",
          url,
          isLive: false,
          hasAudio: false,
          hasVideo: false,
        },
        {
          enableWorker: true, // ✅ 启用worker提升性能
          enableStashBuffer: false, // ✅ 直播禁用缓存
          autoCleanupSourceBuffer: true,
          // fixAudioTimestampGap: true, // ✅ 恢复音视频同步修复
        }
      );

      // 4. 绑定视频元素前必须检查
      flvPlayer.attachMediaElement(videoPlayer.value);

      // 5. 恢复错误处理
      flvPlayer.on("error", (error) => {
        console.error("FLV错误详情:", error);
        alert(`播放错误: ${error.type} - ${error.info}`);
        destroyPlayer();
      });
      console.log(flvPlayer);

      // 6. 加载并播放视频
      flvPlayer.load();
      flvPlayer.play();
    } catch (e: any) {
      console.error("初始化失败:", e);
      alert(`加载失败: ${e.message}`);
    }
  }, 1000);
};

// 7. 修复重复初始化问题
watch(
  () => props.url,
  (newUrl) => {
    if (newUrl) initPlayer(newUrl);
  },
  { immediate: true } // ✅ 已通过watch初始化，无需在onMounted重复调用
);

// 8. 移除重复初始化调用
// onMounted(()=>{
//   initPlayer(props.url) // ❌ 导致重复初始化
// })

onBeforeUnmount(() => {
  destroyPlayer();
});
</script>