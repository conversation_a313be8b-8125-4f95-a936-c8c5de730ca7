// import { createApp } from 'vue'
// const app = createApp({})
const listeners = new Map()

export const eventBus = {
  $emit(event, ...args) {
    const callbacks = listeners.get(event) || []
    callbacks.forEach(cb => cb(...args))
  },
  $on(event, callback) {
    if (!listeners.has(event)) {
      listeners.set(event, [])
    }
    listeners.get(event).push(callback)
  },
  $off(event, callback) {
    const callbacks = listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) callbacks.splice(index, 1)
    }
  }
}

