<template>
  <div class="head">
    <div class="head-sel">
      <div class="head-slot">
        <slot>
        </slot>
      </div>
      <div :class="props.typeShow?'head-sel-arr':'head-sel-arr1'" v-if="props.selShow">
        <div v-for="(item,index) in arr" :key="index" @click="handleClick(index)" :class="selNum==index?'selNum':'noSelnum'">{{ item }}</div>
      </div>
      <div class="adminShow" @click="handleAdminShow" v-if="adminShow">管理</div>
    </div>
    <!-- <video autoplay controls style="videoBg" src="/src/assets/bg.mp4"></video> -->
  </div>
  <modalss ref="modalComponents" :titles="props.title" @change-date="handleChange" :type="2" :dateType="selNum==0?'year':selNum==1?'month':'day'">
  </modalss>
  <modalss ref="modalComponentsVideo" titles="管理摄像头" :type="3" @handleSure="handleSure">

  </modalss>

</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import modalss from "../components/modal.vue";

const props = defineProps({
  selShow: {
    type: Boolean,
    default: true,
  },
  typeShow: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "",
  },
  adminShow: {
    type: Boolean,
    default: false,
  },
});
const modalComponents = ref(null);
const arr = ref(["按年", "按月", "按日"]);
const selNum = ref(0);
const handleClick = (index?: Number) => {
  selNum.value = index;
  emit("changeSelNum", selNum.value + 1);
  modalComponents.value.init();
  modalComponents.value.clearDate();
};

const handleSure = () => {
  emit("handleSure");
};

const emit = defineEmits(["changeDate", "changeSelNum", "handleSure"]);

const handleChange = (date?: any) => {
  emit("changeDate", date);
};
const modalComponentsVideo = ref(null);
const handleAdminShow = () => {
  // emit('handleAdminShow')
  modalComponentsVideo.value.init();
  modalComponentsVideo.value.batchInit();
};
</script>

<style scoped lang="scss">
.head {
  height: 35px;
  background: url("/src/assets/headBg.png") no-repeat;
  position: relative;
  background-size: 100% 100%;
  .adminShow {
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0%;
    color: #96a9ba;
    // margin: 0 10px;
    cursor: pointer;
  }
  .head-sel {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .head-sel-arr,.head-sel-arr1 {
      display: flex;
      // position: relative;
      // bottom: -30px;
      // z-index: 1000;
      .selNum,
      .noSelnum {
        font-weight: 400;
        font-style: Regular;
        font-size: 14px;
        leading-trim: NONE;
        line-height: 100%;
        letter-spacing: 0%;
        color: #96a9ba;
        margin: 0 10px;
        cursor: pointer;
      }
      .selNum {
        color: #ffffff;
        position: relative;
        &::after {
          content: " ";
          width: 50px;
          height: 24px;
          background: url("/src/assets/sel.png") no-repeat;
          background-size: 100% 100%;
          position: absolute;
          left: -10px;
          bottom: -10px;
        }
      }
    }
    .head-sel-arr{
      // display: flex;
      position: relative;
      bottom: -30px;
      z-index: 1000;
    }
  }
  .head-slot {
    // width: 100%;
    font-weight: 700;
    font-style: Bold;
    font-size: 16px;
    color: #ffffff;
    margin-left: 33px;
    // text-align: left;
    margin-top: 3px;
    //   	display: inline-block;
    // white-space: nowrap;
    // width: 50%;
    // overflow: hidden;
    // text-overflow:ellipsis;
  }
}
</style>