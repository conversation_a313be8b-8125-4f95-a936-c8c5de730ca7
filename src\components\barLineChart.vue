<template>
  <div>
    <div class="barLineChart" ref="barLineChart">
  </div>
  <modalss ref="modalComponents" :titles="props.title" :type="1">
  </modalss>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import * as echarts from "echarts";
import modalss from "../components/modal.vue";
import * as api from "../api/index.ts";
import dayjs from "dayjs";

const props = defineProps({
  selShow: {
    type: Boolean,
    default: true,
  },
  title:{
    type:String,
    default:''
  },
  name:{
    type:String,
    default:''
  },
  status:{
    type: Boolean,
    default: true,
  },
});
const modalComponents=ref(null)
const barLineChart = ref(null);
const initBarLineChart = (status?:Boolean) => {
  const chart = echarts.init(barLineChart.value);
  const option = {
    // color: ['#57a1ff','#e58a96'],
    // color: ["#57a1ff", "#FFFFFF"],
    legend: {
      x: "center",
      y: 'bottom',
    //   padding: [0, 0, 0, 25],
      itemWidth: 15, // 图例标记的图形宽度
      itemHeight: 8, // 图例标记的图形高度
      data: [
        { name: "数量", icon: "circle" },
        { name: "档案增量", icon: "rect" },
      ],
      textStyle: {
        color: "#96A9BA",
      },
    },
    grid: {
      left: "10%",
      right: "1%",
    //   bottom: "15%",
      top: "10%",
    },
    //缩放
    dataZoom: [
    ],
    // tooltip: {
    //   trigger: "axis",
    //   backgroundColor: "rgba( 0, 0, 0,0.7)",
    //   borderColor: "rgba( 0, 0, 0,0.7)",
    //   formatter: function (params) {
    //     var str = params[0].name + "</br>";
    //     for (let item of params) {
    //       str = `<span style='color: #fff;'>${str}</span><div style='display:flex;align-items:center;justify-content:space-between;'><span>${item.marker}<span style='color: #fff;'>${item.seriesName}</span></span>&nbsp;&nbsp;&nbsp;&nbsp;<span style='color: #fff;'>${item.value}%</span></div>`;
    //     }
    //     return str;
    //   },
    // },
    xAxis: {
      data: dateData.value,
      axisLabel: {
        show: true,
        color: "#96A9BA",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: "",
        },
      },
    },
    toolbox: {
      // left:'10%',                   // 工具栏组件距离容器左侧的距离
        right:'40%',                  // 工具栏组件距离容器右侧的距离
        // top:'10%', 
      feature: {
        // saveAsImage: {},
        myTool1: {
          show: props.status,
        //   title: "详情",
        top:'10%', 
          icon: "M10.3335 1H15.0002V5.66666 M5.66666 1H1V5.66666 M10.3335 15H15.0002V10.3333 M5.66666 15H1V10.3333 M14.9999 1L9.94434 6.05555 M6.05555 9.94446L1 15",
          onclick: function () {
            // getChartExcel();
            // alert(22)
            modalComponents.value.init()
          },
        //   iconStyle:{
        //     color:'#FFFFF'
        //   }
        },
      },
    //   right: "10%",
    // top:-6
    // iconStyle:{
    //         color:'white',                // 图形颜色
    //         borderWidth:1,              // 图形描边宽度
    //         borderColor:'white',          // 图形描边颜色
    //         borderType:'solid',                // 图形透明度       
    //     },
    },
    yAxis: {
      axisLabel: {
        show: true,
        color: "#96A9BA",
        min: 0,
        max: 100,
        interval: 25,
      },
      splitLine: {
        show: true,
        lineStyle: { type: "dashed",color:"#96A9BA" },
        color: "#96A9BA",
      },
    },
    series: [
      {
        name: "数量",

        type: "bar",
        barWidth: 20,
        data: barData.value,
        borderRadius: [5, 5, 0, 0],
        color: [
          {
            type: "linear",
            x: 1,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              // 设置颜色渐变
              { offset: 0, color: "#035FB8" },
              { offset: 1, color: "#39C2FB" },
            ],
          },
        ],

        itemStyle: {
          borderRadius: [10, 10, 0, 0],
        },
      },
      {
        name: '档案增量',
        type: "line",
        symbol: "none",
        smooth: true,
        // smooth: true,
        itemStyle: {
          normal: {
            color: "#FBE94B",
          },
        },
        data: linkData.value,
      },
    ],
  };
  if(dateData.value.length>6){
    option.dataZoom=[
      {
        type: "slider",
        // show: dateData.value.length<=6?false:true,
        show:true,
        start: 0,
        end: 100,
        height: 10,
        bottom: 40,
        borderColor: "transparent",
        backgroundColor: "#293C47",
        // 拖拽手柄样式 svg 路径
        // handleIcon: 'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
        // handleColor: '#aab6c6',
        // handleSize: 20,
        handleStyle: {
          borderColor: "",
          // shadowBlur: 4,
          // shadowOffsetX: 1,
          // shadowOffsetY: 1,
          shadowColor: "#e5e5e5",
        },
      },
    ]
  }
  option && chart.setOption(option,true);
};
//查询区域占比列表
const dateData=ref([])
const linkData=ref([])
const barData=ref([])
const init=async(record?:any,status?:Boolean)=>{
  setTimeout(async()=>{

    const today = dayjs();
  const data={
     typeId:record.typeId ||'',
    areaId:record.areaId || '',
    dateType:record.dateType,
  }
  if(record.dateType==1){
    data.startTime=record.startTime || today.subtract(status?9:4, 'year').format('YYYY')
    data.endTime=record.endTime || today.format('YYYY')
  }else if(record.dateType==2){
    data.startTime=record.startTime || today.subtract(status?9:4, 'year').format('YYYY-MM')
    data.endTime=record.endTime || today.format('YYYY-MM')
  }else{
    data.startTime=record.startTime || today.subtract(status?9:4, 'year').format('YYYY-MM-DD')
    data.endTime=record.endTime || today.format('YYYY-MM-DD')
  }
  const res=await api.archives_type_increment(data)
  dateData.value=[]
  linkData.value=[]
  barData.value=[]
  res?.data?.forEach(item=>{
    dateData.value.push(item.dateTime)
    linkData.value.push(item.addNum)
    barData.value.push(item.totalNum)
  })
  initBarLineChart()
},100)

}

defineExpose({
  init,
});
onMounted(() => {
  init()
  initBarLineChart();
});
</script>
<style scoped>
.barLineChart {
  width: 100%;
  height: 100%;
}
</style>