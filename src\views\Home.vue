<template>
  <div class="home-container" v-on:keydown="keyDown" tabindex="-1" ref="container">
    <!-- 视频背景 -->
    <video class="background-video" autoplay loop muted playsinline>
      <source src="/src/assets/bg.mp4" type="video/mp4">
      您的浏览器不支持视频标签。
    </video>
    <div class="homeBgH">
      <span>档案数治管理平台</span>
    </div>
    <div class="currentTime">{{ currentTime }}</div>
    <div class="currentDate">{{ currentDate }}</div>
    <div class="home-title">
      上海市市场监督管理局欢迎您
    </div>
  </div>
</template>
<script setup>
import { ref,onMounted } from 'vue';
import { useRouter } from "vue-router";
const currentTime = ref('');
const currentDate = ref('');
const router = useRouter();
const container = ref(null);
// 更新当前时间
setInterval(() => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const year = now.getFullYear()  //年
  const month = now.getMonth() + 1 //月
  const day = now.getDate()    //日
  currentTime.value = `${hours}:${minutes}:${seconds}`;
  currentDate.value = `${year}.${month}.${day}`;
}, 1000);

onMounted(() => {
  // 确保容器获得焦点以接收键盘事件
  sessionStorage.setItem('baseUrl',window.location.hostname)
  container.value.focus();
});

const keyDown=(e)=>{
  e.preventDefault();
  // 根据不同按键执行不同操作
  switch(e.key){
    case 'Enter':
      // 回车键 - 进入详情页
      router.push({path: '/about'});
      break;
  }
}

</script>
<style scoped lang="scss">
.home-container {
  position: relative;
  width: 100vw;
  height: 100%;
  overflow: hidden;
  .home-title{
    // background: url("../assets//home.png") no-repeat; 
    
    width: 1200px;
    height: 150px;
    margin: 0 auto;
    margin-top: 80px;
font-weight: 700;
font-style: Bold;
font-size: 70px;
color:rgba(255, 255, 255, 1);
text-align: center;
line-height: 150px;
  }
  .currentDate {
    font-family: Alimama ShuHeiTi;
font-weight: 700;
font-style: Bold;
font-size: 24px;
leading-trim: NONE;
line-height: 100%;
letter-spacing: 2%;
text-align: center;
// color: var(--2, #96A9BA);
margin-top: 20px;
color: rgba(150, 169, 186, 1);
  }
  .currentTime {
    width: 100%;
    text-align: center;
    font-family: Alimama ShuHeiTi;
    font-weight: 700;
    font-style: Bold;
    font-size: 60px;
    line-height: 100%;

    color: #ffffff;
    margin-top: 177px;
  }
  .homeBgH {
    display: flex;
    justify-content: center;
    // align-items: center;
    width: 100%;
    height: 110px;
    background: url("/src/assets/homeBgH.png") no-repeat;
    background-size: 100% 100%;
    // z-index: 1000;
    span {
      // background: linear-gradient(180deg, #C4D7E6 0%, #FFFFFF 100%);
      font-weight: 700;
      font-style: Bold;
      font-size: 38px;
      leading-trim: NONE;
      line-height: 100%;
      letter-spacing: 8%;
      line-height: 110px;
      color: #ffffff;
      // text-align: center;
    }
  }
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.content {
  position: relative;
  z-index: 1;
}
</style>