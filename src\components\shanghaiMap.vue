<script setup lang="ts" name="Map">
import type { VNode } from "vue";
import { ref, onMounted } from "vue";

import { createVNode, render } from "vue";
import * as echarts from "echarts";
import geoJson from "../assets/shanghai.json"; // 为刚刚下载的json文件，需正确引入路径
import "echarts-gl";
import echartsGL from 'echarts-gl'
import mapBgImg from "../assets/map.png";
const mapChart = ref<HTMLElement>();
const initMap = async () => {
  const chart = echarts.init(mapChart.value);
  echarts.registerMap("nanyang", geoJson);
  const option = {
    series: {
         type: 'map3D',
         map: "nanyang",
         show: true,
         viewControl: {
            distance: 150,
            center: [-2, -5, 0],
         },
         itemStyle: {
            color: "rgba(255,255,255,1)",
            borderWidth: 0.5,
            borderColor: `#539efe9a`,
         },
         shading: "lambert",
         lambertMaterial: {
            //
            detailTexture: `https://img.isqqw.com/profile/upload/2024/08/17/6ee0cfc7-84a7-46a9-8724-8c615862a92c.jpg`,
            // detailTexture: `https://img.isqqw.com/profile/upload/2024/08/19/c713223b-37ea-4ac2-87da-fe66c539d913.jpg`,
         },
         light: {
            main: {
               color: '#fff',
               intensity: 1.2,
               shadow: true,
               shadowQuality: 'ultra',
            },
            ambient: {
               color: 'skyblue',
               intensity: 0.2
            },
         },
         emphasis: {
            label: {
               show: true,
               fontSize: 16,
               color: '#f9fcff',
               fontWeight: 600
            },
            itemStyle: {
               color: '#539efe9a',
            }
         }
      },
  };
  chart.setOption(option);
  // var index=0
  // var mTime=setInterval(()=>{
  //   chart.dispatchAction({
  //     type:'showTip',
  //     seriesIndex:0,
  //     dataIndex:index
  //   })
  // },1000)
  // var mapFeatures = echarts.getMap("centerMap").geoJson.features;
  // var geoCoordMap = {};
  // mapFeatures.forEach(function (v) {
  //   // v.properties.center = _this.convertCoord(v.properties.center)
  //   // 地区名称
  //   var name = v.properties.name;
  //   // 地区经纬度
  //   geoCoordMap[name] = v.properties.center;
  // });
  // let currentIdx = -1;
  // function highlightNext() {
  //   if (currentIdx++ >= 0) {
  //     // 防止下标增加到超过区域数组长度
  //     currentIdx %= mapFeatures.length;
  //     console.log(mapFeatures[currentIdx]);
  //   }
  //   highlightRegion(currentIdx); // 此处的currentIdx经过if判断，已经+1了
  //   setTimeout(highlightNext, 5000);
  // }
  // function highlightRegion(index) {
  //   chart.dispatchAction({
  //               type: 'highlight',
  //               seriesIndex: 0,
  //               dataIndex: index
  //           });
  // }
  chart.on("click", function (params) {
    console.log(params);
    chart.setOption({
      series: {
         data: [
            {
               name: params.name,
               itemStyle: {
                  color: 'yellow'
               },
               label:{
                  show:true
               }
            }
         ],
      }
   })

  });
  chart.getZr().on("click", (params) => {
    console.log(params);

    if (params.target) {
    } 
  });
};
onMounted(() => {
  // 初始化地图
  initMap();
});
</script>
 
<template>
  <div ref="mapChart" class="mapChart" style="width: 100%;height: 537px;" />
</template>
 
<style lang="scss" scoped>
</style>